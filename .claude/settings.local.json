{"permissions": {"allow": ["Bash(bundle install:*)", "Bash(bundle exec rails generate:*)", "Bash(bundle exec rails:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "<PERSON><PERSON>(pkill:*)", "Bash(bin/rails test:*)", "Bash(bin/rails generate:*)", "Bash(bin/rails:*)", "Bash(grep:*)", "Bash(rails generate:*)", "Bash(rm:*)", "<PERSON><PERSON>(touch:*)", "Bash(bundle exec rspec:*)", "Bash(find:*)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "Bash(ruby:*)", "Bash(rg:*)", "Bash(rails routes)", "Bash(rails db:migrate)", "Bash(rails server:*)", "<PERSON><PERSON>(rails runner:*)", "Bash(rubocop:*)", "<PERSON><PERSON>(rspec:*)", "<PERSON><PERSON>(sed:*)", "Bash(rails console:*)", "Bash(ls:*)", "Bash(erb:*)"], "deny": []}}