class VideoCompressionJob < ApplicationJob
  queue_as :default

  def perform(model_type, model_id, attachment_name)
    model = model_type.constantize.find(model_id)
    attachment = model.send(attachment_name)
    
    return unless attachment.attached?
    return unless video_file?(attachment)

    Rails.logger.info "Starting video compression for #{model_type}##{model_id}"

    # Create temporary files
    input_path = create_temp_file(attachment)
    output_path = create_output_path(input_path)

    begin
      # Compress video using FFMPEG
      compress_video(input_path, output_path)
      
      # Replace the original attachment with compressed version
      replace_attachment(model, attachment_name, output_path, attachment.filename.to_s)
      
      Rails.logger.info "Video compression completed for #{model_type}##{model_id}"
      
    rescue StandardError => e
      Rails.logger.error "Video compression failed for #{model_type}##{model_id}: #{e.message}"
      raise e
    ensure
      # Clean up temporary files
      cleanup_temp_files(input_path, output_path)
    end
  end

  private

  def video_file?(attachment)
    attachment.content_type&.start_with?('video/')
  end

  def create_temp_file(attachment)
    temp_file = Tempfile.new(['video_input', File.extname(attachment.filename.to_s)])
    temp_file.binmode
    temp_file.write(attachment.download)
    temp_file.close
    temp_file.path
  end

  def create_output_path(input_path)
    dir = File.dirname(input_path)
    basename = File.basename(input_path, '.*')
    File.join(dir, "#{basename}_compressed.mp4")
  end

  def compress_video(input_path, output_path)
    require 'streamio-ffmpeg'
    
    movie = FFMPEG::Movie.new(input_path)
    
    # Standard compression options for good quality/size balance
    options = {
      video_codec: 'libx264',
      audio_codec: 'aac',
      video_bitrate: '1000k',
      audio_bitrate: '128k',
      resolution: '1280x720',
      frame_rate: 30,
      custom: [
        '-preset', 'medium',
        '-crf', '23',
        '-movflags', '+faststart'
      ]
    }
    
    movie.transcode(output_path, options)
  end

  def replace_attachment(model, attachment_name, compressed_path, original_filename)
    # Generate new filename with compressed suffix if not already .mp4
    new_filename = if original_filename.downcase.end_with?('.mp4')
                     original_filename
                   else
                     "#{File.basename(original_filename, '.*')}_compressed.mp4"
                   end

    # Attach the compressed video
    File.open(compressed_path, 'rb') do |file|
      model.send(attachment_name).attach(
        io: file,
        filename: new_filename,
        content_type: 'video/mp4'
      )
    end
  end

  def cleanup_temp_files(*paths)
    paths.compact.each do |path|
      File.delete(path) if File.exist?(path)
    rescue StandardError => e
      Rails.logger.warn "Failed to cleanup temp file #{path}: #{e.message}"
    end
  end
end