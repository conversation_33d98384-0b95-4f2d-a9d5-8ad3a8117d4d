module Vendors
  class ProductsController < BaseController
    before_action :set_product, only: [:show, :edit, :update, :destroy]

    def index
      @products = current_account.products.includes(:category)
                                .order(created_at: :desc)
    end

    def show
      @product_videos = @product.product_videos.order(:position)
      @case_studies = @product.case_studies.order(:position)
      @product_content = @product.product_content.order(:position)
    end

    def new
      @product = current_account.products.build
      @categories = Category.alphabetical
    end

    def create
      @product = current_account.products.build(product_params)

      if @product.save
        redirect_to vendors_product_path(@product), notice: 'Product was successfully created.'
      else
        @categories = Category.alphabetical
        render :new, status: :unprocessable_entity
      end
    end

    def edit
      @categories = Category.alphabetical
    end

    def update
      if @product.update(product_params)
        redirect_to vendors_product_path(@product), notice: 'Product was successfully updated.'
      else
        @categories = Category.alphabetical
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @product.destroy!
      redirect_to vendors_products_path, notice: 'Product was successfully deleted.'
    end

    private

    def set_product
      @product = current_account.products.friendly.find(params[:id])
    end

    def product_params
      permitted_params = params.require(:product).permit(
        :name, :description, :pricing_model, :published, :category_id
      )

      # Handle features parameter - convert from form input to JSON array
      if params[:product][:features_input].present?
        feature_names = params[:product][:features_input].split(',').map(&:strip).reject(&:blank?)
        permitted_params[:features] = feature_names.map { |name| { "name" => name } }
      else
        permitted_params[:features] = []
      end

      permitted_params
    end
  end
end