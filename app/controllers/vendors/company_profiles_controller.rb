module Vendors
  class CompanyProfilesController < BaseController
    before_action :set_company_profile, except: [:new, :create]
    before_action :ensure_company_profile, only: [:show, :edit, :update, :destroy]

    def show
    end

    def new
      @company_profile = current_account.build_company_profile
    end

    def create
      @company_profile = current_account.build_company_profile(company_profile_params)

      if @company_profile.save
        redirect_to vendors_company_profile_path, notice: 'Company profile was successfully created.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @company_profile.update(company_profile_params)
        redirect_to vendors_company_profile_path, notice: 'Company profile was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @company_profile.destroy!
      redirect_to vendors_company_profile_path, notice: 'Company profile was successfully deleted.'
    end

    def update_logo
      Rails.logger.debug "All params: #{params.inspect}"
      
      logo_file = params.dig(:company_profile, :logo)
      
      if logo_file.present?
        Rails.logger.debug "Logo file present: #{logo_file.inspect}"
        
        # Manually validate the logo file
        if logo_file.size > 5.megabytes
          flash[:alert] = "Logo file must be less than 5MB"
          render :edit, status: :unprocessable_entity
          return
        end
        
        allowed_types = %w[image/jpeg image/jpg image/png image/gif image/webp image/svg+xml]
        unless allowed_types.include?(logo_file.content_type)
          flash[:alert] = "Logo must be a valid image format (JPEG, PNG, GIF, WebP, or SVG)"
          render :edit, status: :unprocessable_entity
          return
        end
        
        begin
          @company_profile.logo.attach(logo_file)
          redirect_to vendors_company_profile_path, notice: 'Company logo updated successfully.'
        rescue => e
          Rails.logger.error "Error attaching logo: #{e.message}"
          flash[:alert] = "There was an error uploading the logo. Please try again."
          render :edit, status: :unprocessable_entity
        end
      else
        Rails.logger.debug "No logo file provided"
        flash[:alert] = "Please select a logo file to upload."
        render :edit, status: :unprocessable_entity
      end
    end

    def remove_logo
      @company_profile.logo.purge if @company_profile.logo.attached?
      redirect_to vendors_company_profile_path, notice: 'Company logo removed successfully.'
    end

    private

    def set_company_profile
      @company_profile = current_account.company_profile
    end

    def ensure_company_profile
      redirect_to new_vendors_company_profile_path unless @company_profile
    end

    def company_profile_params
      params.require(:company_profile).permit(
        :company_name, :website, :company_size,
        :headquarters_location, :contact_email, :contact_phone,
        :linkedin_url, :twitter_url, :published, :description,
        :logo
      )
    end

    def logo_params
      if params[:company_profile].present?
        params.require(:company_profile).permit(:logo)
      else
        {}
      end
    end
  end
end