module Vendors
  class LeadsController < BaseController
    before_action :set_lead, only: [:show, :update]

    def index
      @leads = current_account.leads.includes(:product, :agency_account, :user)
                             .order(created_at: :desc)
      
      # Filter by status
      if params[:status].present?
        @leads = @leads.where(status: params[:status])
      end
      
      # Filter by product
      if params[:product_id].present?
        @leads = @leads.where(product_id: params[:product_id])
      end
      
      # Search by contact info or message
      if params[:search].present?
        search_term = "%#{params[:search]}%"
        @leads = @leads.where(
          "LOWER(contact_name) LIKE LOWER(?) OR LOWER(contact_email) LIKE LOWER(?) OR LOWER(message) LIKE LOWER(?)",
          search_term, search_term, search_term
        )
      end
      
      @products = current_account.products.order(:name)
      @lead_stats = {
        total: current_account.leads.count,
        pending: current_account.leads.where(status: 'pending').count,
        contacted: current_account.leads.where(status: 'contacted').count,
        qualified: current_account.leads.where(status: 'qualified').count,
        closed: current_account.leads.where(status: 'closed').count
      }
    end

    def show
    end

    def update
      if @lead.update(lead_params)
        respond_to do |format|
          format.html { redirect_to vendors_lead_path(@lead), notice: 'Lead updated successfully.' }
          format.json { render json: { status: 'success', new_status: @lead.status, new_status_humanized: @lead.status.humanize } }
        end
      else
        respond_to do |format|
          format.html { render :show, status: :unprocessable_entity }
          format.json { render json: { status: 'error', errors: @lead.errors.full_messages }, status: :unprocessable_entity }
        end
      end
    end

    private

    def set_lead
      @lead = current_account.leads.find(params[:id])
    end

    def lead_params
      params.require(:lead).permit(:status, :notes)
    end
  end
end