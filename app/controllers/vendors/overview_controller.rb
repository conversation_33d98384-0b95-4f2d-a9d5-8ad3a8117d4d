module Vendors
  class OverviewController < BaseController
    def index
      @company_profile = current_account.company_profile
      @products = current_account.products.limit(5)
      @recent_leads = Lead.joins(:product)
                         .where(products: { account: current_account })
                         .includes(:user, :product)
                         .recent
                         .limit(10)
      @stats = {
        products_count: current_account.products.count,
        published_products: current_account.products.published.count,
        total_leads: Lead.joins(:product).where(products: { account: current_account }).count,
        new_leads: Lead.joins(:product).where(products: { account: current_account }, status: 'pending').count
      }
    end
  end
end