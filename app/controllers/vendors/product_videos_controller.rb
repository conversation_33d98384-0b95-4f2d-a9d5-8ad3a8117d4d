module Vendors
  class ProductVideosController < BaseController
    before_action :set_product
    before_action :set_product_video, only: [:show, :edit, :update, :destroy, :update_position]
    before_action :ensure_owner

    def index
      @product_videos = @product.product_videos.by_position.with_attached_video_file
    end

    def show
    end

    def new
      @product_video = @product.product_videos.build
      set_next_position
    end

    def create
      @product_video = @product.product_videos.build(product_video_params)
      set_next_position unless @product_video.position

      if @product_video.save
        redirect_to vendors_product_product_videos_path(@product), notice: 'Video was successfully added.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @product_video.update(product_video_params)
        redirect_to vendors_product_product_videos_path(@product), notice: 'Video was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @product_video.destroy!
      redirect_to vendors_product_path(@product), notice: 'Video was successfully deleted.'
    end

    def update_position
      new_position = params[:position].to_i
      
      # Reorder other videos to make room for the moved video
      ProductVideo.transaction do
        # Get all videos for this product in current order
        videos = @product.product_videos.order(:position)
        
        # Remove the video being moved from its current position
        videos_without_moved = videos.reject { |v| v.id == @product_video.id }
        
        # Insert the moved video at the new position
        videos_without_moved.insert(new_position - 1, @product_video)
        
        # Update positions for all videos
        videos_without_moved.each_with_index do |video, index|
          video.update!(position: index + 1)
        end
      end
      
      render json: { status: 'success' }
    rescue => e
      render json: { status: 'error', errors: [e.message] }
    end

    private

    def set_product
      @product = current_account.products.friendly.find(params[:product_id])
    end

    def set_product_video
      @product_video = @product.product_videos.find(params[:id])
    end

    def ensure_owner
      redirect_to root_path unless @product.account == current_account
    end

    def set_next_position
      @product_video.position = (@product.product_videos.maximum(:position) || 0) + 1
    end

    def product_video_params
      params.require(:product_video).permit(:title, :description, :published, :video_file)
    end
  end
end