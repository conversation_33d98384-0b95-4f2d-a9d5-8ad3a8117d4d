module Vendors
  class BaseController < ApplicationController
    before_action :require_authentication
    before_action :require_vendor_account
    before_action :require_approved_account

    layout 'dashboard'

    protected

    def current_account
      @current_account ||= current_user.accounts.vendors.find_by(id: session[:account_id]) ||
                          current_user.accounts.vendors.first
    end
  end
end