module Agencies
  class DashboardController < BaseController
    def index
      @recent_interests = current_account.agency_leads.includes(:product, :account)
                                        .order(created_at: :desc)
                                        .limit(5)
      
      @interest_stats = {
        total: current_account.agency_leads.count,
        pending: current_account.agency_leads.where(status: 'pending').count,
        contacted: current_account.agency_leads.where(status: 'contacted').count,
        qualified: current_account.agency_leads.where(status: 'qualified').count
      }
      
      @featured_companies = CompanyProfile.published.limit(6)
      @featured_products = Product.published.includes(:account).limit(6)
    end
  end
end