module Agencies
  class InterestsController < BaseController
    allow_unauthenticated_access only: [:create]
    before_action :set_product, only: [:create]

    def create
      @lead = Lead.new(lead_params)
      @lead.product = @product
      @lead.account = @product.account
      @lead.agency_account = current_account if current_user
      @lead.user = current_user || User.find_or_create_by(email_address: @lead.contact_email) do |u|
        u.first_name = @lead.contact_name.split(' ').first
        u.last_name = @lead.contact_name.split(' ')[1..-1].join(' ') if @lead.contact_name.split(' ').length > 1
        u.password = SecureRandom.urlsafe_base64(12)
      end
      @lead.source = 'marketplace'
      @lead.status = 'pending'

      if @lead.save
        # Send notification email to vendor
        LeadMailer.new_lead_notification(@lead).deliver_later
        
        redirect_to marketplace_product_path(@product), 
                    notice: 'Your interest has been submitted! The vendor will be notified and will contact you soon.'
      else
        redirect_to marketplace_product_path(@product), 
                    alert: 'There was an error submitting your interest. Please try again.'
      end
    end

    def index
      @leads = current_account.agency_leads.includes(:product, :account).order(created_at: :desc)
    end

    private

    def set_product
      @product = Product.published.friendly.find(params[:product_id])
    end

    def lead_params
      params.require(:lead).permit(:message, :contact_name, :contact_email, :contact_phone, :contact_company, :timeline, :budget_range)
    end
  end
end