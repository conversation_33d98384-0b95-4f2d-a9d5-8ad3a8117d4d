module Agencies
  class BaseController < ApplicationController
    before_action :require_authentication
    before_action :require_agency_account
    before_action :require_approved_account

    layout 'dashboard'

    protected

    def current_account
      @current_account ||= current_user.accounts.agencies.find_by(id: session[:account_id]) ||
                          current_user.accounts.agencies.first
    end
  end
end