class InvitationAcceptancesController < ApplicationController
  allow_unauthenticated_access

  before_action :set_invitation, only: [ :show, :accept, :decline ]
  before_action :check_invitation_validity, only: [ :show, :accept, :decline ]

  def show
    # Display invitation details and acceptance options
    # Handle direct accept/decline links from email
    if params[:token] && request.path.include?('/accept')
      @action = 'accept'
    elsif params[:token] && request.path.include?('/decline')
      @action = 'decline'
    else
      @action = 'show'
    end
    
    # Check if user exists with this email
    @existing_user = User.find_by(email_address: @invitation.email)
    
    # If no existing user, prepare a new user instance for the form
    unless @existing_user
      @user = User.new(
        email_address: @invitation.email,
        first_name: '',
        last_name: '',
        job_title: ''
      )
    end
  end

  def accept
    if current_user
      # User is already logged in
      if @invitation.accept!(current_user)
        redirect_to root_path, notice: "Welcome to the team! You've successfully joined #{@invitation.account.name}."
      else
        redirect_to @invitation, alert: "Unable to accept invitation. You may already be a member of this account."
      end
    else
      # Check if user exists
      existing_user = User.find_by(email_address: @invitation.email)
      
      if existing_user
        # User exists, they need to sign in
        session[:invitation_token] = @invitation.token
        redirect_to new_session_path, notice: "Please sign in to accept this invitation."
      else
        # User doesn't exist, create them if user params are provided
        if params[:user].present?
          @user = User.new(user_params)
          @user.email_address = @invitation.email
          
          if @user.save
            # User created successfully, accept the invitation
            if @invitation.accept!(@user)
              # Log the user in
              start_new_session_for(@user)
              redirect_to root_path, notice: "Welcome! Your account has been created and you've joined #{@invitation.account.name}."
            else
              redirect_to @invitation, alert: "Unable to accept invitation. Please try again."
            end
          else
            # User creation failed, show form again with errors
            @existing_user = nil
            @action = 'accept'
            render :show
          end
        else
          # No user params, redirect back to form
          redirect_to accept_invitation_path(@invitation.token), alert: "Please fill in your information to create an account."
        end
      end
    end
  end

  def decline
    @invitation.decline!
    redirect_to root_path, notice: "You have declined the invitation to join #{@invitation.account.name}."
  end

  private

  def set_invitation
    @invitation = TeamInvitation.find_by(token: params[:token])
    unless @invitation
      redirect_to root_path, alert: "Invalid invitation link."
    end
  end

  def user_params
    params.require(:user).permit(:first_name, :last_name, :job_title, :password, :password_confirmation)
  end

  def check_invitation_validity
    return unless @invitation

    if @invitation.expired?
      @invitation.update!(status: "expired")
      redirect_to root_path, alert: "This invitation has expired."
    elsif @invitation.status != "pending"
      case @invitation.status
      when "accepted"
        redirect_to root_path, notice: "This invitation has already been accepted."
      when "declined"
        redirect_to root_path, notice: "This invitation has already been declined."
      when "expired"
        redirect_to root_path, alert: "This invitation has expired."
      end
    end
  end
end
