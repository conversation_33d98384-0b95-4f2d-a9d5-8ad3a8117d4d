class MarketplaceController < ApplicationController
  allow_unauthenticated_access
  
  helper_method :can_view_product_content?

  def index
    @categories = Category.all
    
    # Featured content for Airbnb-style home
    @featured_companies = CompanyProfile.published.includes(:account)
                                       .order(created_at: :desc)
                                       .limit(8)
    
    @featured_products = Product.published.includes(:account, :category)
                               .order(created_at: :desc)
                               .limit(12)
    
    @recently_added_companies = CompanyProfile.published.includes(:account)
                                             .where('created_at >= ?', 30.days.ago)
                                             .order(created_at: :desc)
                                             .limit(6)
    
    @recently_added_products = Product.published.includes(:account, :category)
                                     .where('created_at >= ?', 30.days.ago)
                                     .order(created_at: :desc)
                                     .limit(8)
    
    @recently_added_case_studies = CaseStudy.joins(product: :account)
                                           .where(products: { published: true })
                                           .where('case_studies.created_at >= ?', 30.days.ago)
                                           .includes(product: [:account, :category])
                                           .order(created_at: :desc)
                                           .limit(6)
    
    # Popular features removed since features are now product-specific JSON
  end

  def companies
    @companies = CompanyProfile.published.includes(:account)
    
    # Search functionality
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      @companies = @companies.where("company_name LIKE ?", search_term)
    end
    
    # Company size filter
    if params[:company_size].present?
      @companies = @companies.where(company_size: params[:company_size])
    end

    
    @companies = @companies.order(:company_name)
    @company_sizes = CompanyProfile::COMPANY_SIZES
  end

  def products
    @products = Product.published.includes(:account, :category, :case_studies)

    # Get categories for filtering
    @categories = Category.all
    
    # Comprehensive search functionality across multiple fields
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      @products = @products.left_joins(:account)
                          .left_joins(account: :company_profile)
                          .where(
                            "products.name LIKE ? OR company_profiles.company_name LIKE ?", 
                            search_term, search_term
                          )
    end
    
    # Category filter (multiple selection support)
    if params[:category_ids].present?
      category_ids = params[:category_ids].reject(&:blank?)
      if category_ids.any?
        @products = @products.where(category_id: category_ids)
      end
    elsif params[:category_id].present?
      @products = @products.where(category_id: params[:category_id])
    end
    
    # Feature filtering removed since features are now product-specific JSON
    
    # Pricing model filter
    if params[:pricing_model].present?
      @products = @products.where(pricing_model: params[:pricing_model])
    end
    
    # Company size filter
    if params[:company_size].present?
      @products = @products.joins(account: :company_profile)
                         .where(company_profiles: { company_size: params[:company_size] })
    end
    
    # Sort options
    case params[:sort]
    when 'newest'
      @products = @products.order(created_at: :desc)
    when 'oldest'
      @products = @products.order(created_at: :asc)
    when 'name_asc'
      @products = @products.order(:name)
    when 'name_desc'
      @products = @products.order(name: :desc)
    else
      @products = @products.order(:name)
    end
    
    @pricing_models = Product.pricing_models.keys
    @company_sizes = CompanyProfile::COMPANY_SIZES rescue ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']
    @sort_options = [
      ['Name (A-Z)', 'name_asc'],
      ['Name (Z-A)', 'name_desc'],
      ['Newest First', 'newest'],
      ['Oldest First', 'oldest']
    ]
  end

  def company
    @company = CompanyProfile.published.friendly.find(params[:id])
    @products = @company.account.products.published.includes(:category)
  end

  def product
    @product = Product.published.includes(:category, :product_videos, :case_studies, :product_content)
                      .friendly.find(params[:id])
    @company = @product.account.company_profile
    @related_products = @product.account.products.published.where.not(id: @product.id)
                               .includes(:category).limit(3)
    
    # Get demo videos ordered by position
    @product_videos = @product.product_videos.order(:position)
    @demo_video = @product_videos.first
    
    # Get case studies for this product
    @case_studies = @product.case_studies.order(:position)
    
    # Get additional content/media
    @product_content = @product.product_content.order(:position)
    
    # Check if current user has already expressed interest (only if authenticated)
    @existing_interest = current_account&.agency_leads&.find_by(product: @product) if current_account
    
    # Initialize new lead for the interest form
    @lead = Lead.new
  end

  def product_video
    @product = Product.published.friendly.find(params[:product_id])
    require_content_access(@product)
    @product_video = @product.product_videos.find(params[:id])
    @company = @product.account.company_profile
  end

  def case_study
    @product = Product.published.friendly.find(params[:product_id])
    require_content_access(@product)
    @case_study = @product.case_studies.find(params[:id])
    @company = @product.account.company_profile
  end

  def product_content
    @product = Product.published.friendly.find(params[:product_id])
    require_content_access(@product)
    @product_content = @product.product_content.find(params[:id])
    @company = @product.account.company_profile
  end

  private

  def can_view_product_content?(product)
    return false unless current_user
    
    begin
      # Super admins can view everything
      return true if current_user.super_admin?
      
      # Agency users (with approved agency accounts) can view content
      if current_user.has_agency_account?
        approved_agency_accounts = current_user.accounts.where(account_type: 'agency', status: 'approved')
        return true if approved_agency_accounts.any?
      end
      
      # Product owner (vendor account that owns this product) can view their own content
      return true if product.account.users.include?(current_user)
      
      false
    rescue => e
      Rails.logger.error "Error in can_view_product_content?: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end
  
  def require_content_access(product)
    unless can_view_product_content?(product)
      redirect_to marketplace_product_path(product), 
                  alert: "Access denied. Only approved government agencies and departments can view this content."
    end
  end
end