class ErrorsController < ApplicationController
  allow_unauthenticated_access
  
  def not_found
    @error_code = 404
    @error_title = "Page Not Found"
    @error_message = "The page you're looking for doesn't exist or has been moved."
    
    respond_to do |format|
      format.html { render status: 404 }
      format.json { render json: { error: @error_message }, status: 404 }
    end
  end

  def unprocessable_entity
    @error_code = 422
    @error_title = "Unable to Process Request"
    @error_message = "The request could not be processed due to invalid data."
    
    respond_to do |format|
      format.html { render status: 422 }
      format.json { render json: { error: @error_message }, status: 422 }
    end
  end

  def internal_server_error
    @error_code = 500
    @error_title = "Internal Server Error"
    @error_message = "Something went wrong on our end. We've been notified and are working to fix it."
    
    respond_to do |format|
      format.html { render status: 500 }
      format.json { render json: { error: @error_message }, status: 500 }
    end
  end
end