class Accounts::TeamMembersController < ApplicationController
  include Authentication
  
  before_action :require_authentication
  before_action :set_account
  before_action :set_team_member, only: [:edit, :update, :update_role, :destroy]
  before_action :require_account_access
  before_action :require_account_approval, except: [:index]
  before_action :require_admin_role

  def index
    @team_members = @account.account_users.includes(:user).order(:role, :joined_at)
    @pending_invitations = TeamInvitation.where(account: @account, status: 'pending')
  end

  def edit
  end

  def update
    if @team_member.update(team_member_params)
      redirect_to account_team_members_path, notice: 'Team member updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def update_role
    if @team_member.update(role: params[:account_user][:role])
      redirect_to account_team_members_path, notice: 'Team member role updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @team_member.user == @account.owner
      redirect_to account_team_members_path, alert: 'Cannot remove the account owner.'
      return
    end

    @team_member.destroy!
    redirect_to account_team_members_path, notice: 'Team member removed successfully.'
  end

  private

  def set_account
    @account = current_user.accounts.first
    unless @account
      redirect_to account_path, alert: 'No account found for team management.'
    end
  end

  def set_team_member
    @team_member = @account.account_users.find(params[:id])
  end

  def require_account_access
    unless @account && (@account.users.include?(current_user) || current_user.admin?)
      redirect_to account_path, alert: 'You do not have access to manage this account\'s team.'
    end
  end

  def require_account_approval
    unless @account&.approved? || current_user.admin?
      redirect_to account_path, alert: 'Your account must be approved before you can manage team members.'
    end
  end

  def require_admin_role
    current_user_account = @account.account_users.find_by(user: current_user)
    unless current_user_account&.role == 'admin' || @account.owner == current_user || current_user.admin?
      redirect_to root_path, alert: 'You must be an admin to access team management.'
    end
  end

  def team_member_params
    params.require(:account_user).permit(:role)
  end
end