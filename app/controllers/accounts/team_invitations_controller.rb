class Accounts::TeamIn<PERSON>s<PERSON>ontroller < ApplicationController
  include Authentication

  before_action :require_authentication
  before_action :set_account
  before_action :set_team_invitation, only: [ :destroy ]
  before_action :require_account_access
  before_action :require_account_approval
  before_action :require_admin_role

  def index
    redirect_to account_team_members_path
  end

  def new
    @team_invitation = TeamInvitation.new
  end

  def create
    @team_invitation = @account.team_invitations.build(invitation_params)
    @team_invitation.invited_by = current_user
    @team_invitation.token = SecureRandom.urlsafe_base64(32)
    @team_invitation.expires_at = 7.days.from_now

    if @team_invitation.save
      # Send invitation email
      TeamInvitationMailer.invitation_email(@team_invitation).deliver_later
      redirect_to account_team_members_path, notice: "Team invitation sent successfully."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
    @team_invitation.destroy!
    redirect_to account_team_members_path, notice: "Invitation cancelled successfully."
  end

  private

  def set_account
    @account = current_user.accounts.first
    unless @account
      redirect_to account_path, alert: "No account found for team management."
    end
  end

  def set_team_invitation
    @team_invitation = @account.team_invitations.find(params[:id])
  end

  def require_account_access
    unless @account && (@account.users.include?(current_user) || current_user.admin?)
      redirect_to account_path, alert: "You do not have access to manage this account's team."
    end
  end

  def require_account_approval
    unless @account&.approved? || current_user.admin?
      redirect_to account_path, alert: "Your account must be approved before you can invite team members."
    end
  end

  def require_admin_role
    current_user_account = @account.account_users.find_by(user: current_user)
    unless current_user_account&.role == "admin" || @account.owner == current_user || current_user.admin?
      redirect_to root_path, alert: "You must be an admin to invite team members."
    end
  end

  def invitation_params
    params.require(:team_invitation).permit(:email, :role, :message)
  end
end
