class AccountsController < ApplicationController
  include Authentication
  
  before_action :require_authentication
  before_action :set_user
  
  layout 'dashboard'

  def show
  end

  def edit
  end

  def update
    if @user.update(user_params)
      redirect_to account_path, notice: 'Profile updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def update_profile
    if @user.update(profile_params)
      redirect_to account_path, notice: 'Profile updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def update_password
    if params[:user][:password].blank?
      redirect_to edit_account_path, alert: 'Password cannot be blank.'
    elsif @user.update(password_params)
      redirect_to account_path, notice: 'Password updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def update_profile_photo
    if @user.update(profile_photo_params)
      redirect_to account_path, notice: 'Profile photo updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def remove_profile_photo
    @user.profile_photo.purge if @user.profile_photo.attached?
    redirect_to account_path, notice: 'Profile photo removed successfully.'
  end


  protected

  def current_account
    @current_account ||= current_user.accounts.find_by(id: session[:account_id]) ||
                        current_user.accounts.first
  end

  private

  def set_user
    @user = current_user
  end

  def user_params
    params.require(:user).permit(:first_name, :last_name, :email_address, :phone, :job_title, :password, :password_confirmation)
  end

  def profile_params
    params.require(:user).permit(:first_name, :last_name, :email_address, :phone, :job_title)
  end

  def password_params
    params.require(:user).permit(:password, :password_confirmation)
  end

  def profile_photo_params
    params.require(:user).permit(:profile_photo)
  end
end