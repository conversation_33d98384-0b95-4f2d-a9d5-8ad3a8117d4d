module Admin
  class ProductContentsController < BaseController
    before_action :set_product
    before_action :set_product_content, only: [:show, :edit, :update, :destroy, :update_position]

    def index
      @product_contents = @product.product_content.by_position
    end

    def show
    end

    def new
      @product_content = @product.product_content.build
    end

    def create
      @product_content = @product.product_content.build(product_content_params)
      
      if @product_content.save
        redirect_to admin_product_product_contents_path(@product), notice: 'Product content was successfully created.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @product_content.update(product_content_params)
        redirect_to admin_product_product_contents_path(@product), notice: 'Product content was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @product_content.destroy!
      redirect_to admin_product_product_contents_path(@product), notice: 'Product content was successfully deleted.'
    end

    def update_position
      @product_content.update!(position: params[:position])
      head :ok
    end

    private

    def set_product
      @product = Product.friendly.find(params[:product_id])
    end

    def set_product_content
      @product_content = @product.product_content.find(params[:id])
    end

    def product_content_params
      params.require(:product_content).permit(:title, :description, :file, :published)
    end
  end
end