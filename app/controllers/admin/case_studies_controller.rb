module Admin
  class CaseStudiesController < BaseController
    before_action :set_product
    before_action :set_case_study, only: [:show, :edit, :update, :destroy, :update_position]

    def index
      @case_studies = @product.case_studies.by_position
    end

    def show
    end

    def new
      @case_study = @product.case_studies.build
    end

    def create
      @case_study = @product.case_studies.build(case_study_params)
      
      if @case_study.save
        redirect_to admin_product_case_studies_path(@product), notice: 'Case study was successfully created.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @case_study.update(case_study_params)
        redirect_to admin_product_case_studies_path(@product), notice: 'Case study was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @case_study.destroy!
      redirect_to admin_product_case_studies_path(@product), notice: 'Case study was successfully deleted.'
    end

    def update_position
      @case_study.update!(position: params[:position])
      head :ok
    end

    private

    def set_product
      @product = Product.friendly.find(params[:product_id])
    end

    def set_case_study
      @case_study = @product.case_studies.find(params[:id])
    end

    def case_study_params
      params.require(:case_study).permit(:title, :description, :upload, :published)
    end
  end
end