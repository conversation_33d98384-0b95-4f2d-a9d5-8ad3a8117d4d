module Admin
  class ProductVideosController < BaseController
    before_action :set_product
    before_action :set_product_video, only: [:show, :edit, :update, :destroy, :update_position]

    def index
      @product_videos = @product.product_videos.by_position
    end

    def show
    end

    def new
      @product_video = @product.product_videos.build
    end

    def create
      @product_video = @product.product_videos.build(product_video_params)
      
      if @product_video.save
        redirect_to admin_product_product_videos_path(@product), notice: 'Product video was successfully created.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @product_video.update(product_video_params)
        redirect_to admin_product_product_videos_path(@product), notice: 'Product video was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @product_video.destroy!
      redirect_to admin_product_product_videos_path(@product), notice: 'Product video was successfully deleted.'
    end

    def update_position
      @product_video.update!(position: params[:position])
      head :ok
    end

    private

    def set_product
      @product = Product.friendly.find(params[:product_id])
    end

    def set_product_video
      @product_video = @product.product_videos.find(params[:id])
    end

    def product_video_params
      params.require(:product_video).permit(:title, :description, :video_file, :published)
    end
  end
end