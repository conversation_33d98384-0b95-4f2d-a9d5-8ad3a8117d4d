module Admin
  class EmailPreviewsController < BaseController
    def index
      @email_previews = available_previews
    end

    def show
      preview_class = params[:id].camelize + "Preview"
      
      begin
        @preview = preview_class.constantize.new
        @email_method = params[:email_method] || @preview.emails.first
        
        if @preview.respond_to?(@email_method)
          @email = @preview.public_send(@email_method)
          
          # Handle different response formats
          respond_to do |format|
            format.html do
              if params[:part] == 'text'
                render plain: @email.text_part&.body || @email.body
              else
                render html: (@email.html_part&.body || @email.body).to_s.html_safe
              end
            end
            format.json do
              render json: {
                subject: @email.subject,
                to: @email.to,
                from: @email.from,
                html: @email.html_part&.body&.to_s,
                text: @email.text_part&.body&.to_s || @email.body.to_s
              }
            end
          end
        else
          redirect_to admin_email_previews_path, alert: "Email method '#{@email_method}' not found"
        end
      rescue NameError
        redirect_to admin_email_previews_path, alert: "Email preview '#{preview_class}' not found"
      rescue => e
        redirect_to admin_email_previews_path, alert: "Error loading email preview: #{e.message}"
      end
    end

    private

    def available_previews
      previews = []
      
      # Scan for preview classes
      Dir[Rails.root.join('test/mailers/previews/*.rb'), Rails.root.join('spec/mailers/previews/*.rb')].each do |file|
        class_name = File.basename(file, '.rb').camelize
        begin
          klass = class_name.constantize
          if klass.respond_to?(:emails)
            previews << {
              name: class_name.gsub(/Preview$/, ''),
              class_name: class_name,
              emails: klass.emails,
              description: klass.try(:description) || "Email previews for #{class_name.gsub(/Preview$/, '')}"
            }
          end
        rescue
          # Skip files that can't be loaded
        end
      end

      # Add AccountConfirmationMailer if not found
      unless previews.any? { |p| p[:class_name] == 'AccountConfirmationMailerPreview' }
        previews << {
          name: 'AccountConfirmationMailer',
          class_name: 'AccountConfirmationMailerPreview',
          emails: ['confirmation_email'],
          description: 'Account confirmation email notifications'
        }
      end

      # Add built-in previews if they don't exist
      if previews.empty?
        previews = create_sample_previews
      end

      previews.sort_by { |p| p[:name] }
    end

    def create_sample_previews
      [
        {
          name: 'User',
          class_name: 'UserPreview',
          emails: ['welcome', 'account_approved', 'account_rejected', 'password_reset'],
          description: 'User account related email notifications'
        },
        {
          name: 'Lead',
          class_name: 'LeadPreview', 
          emails: ['new_lead_notification', 'lead_status_update'],
          description: 'Lead and inquiry related email notifications'
        },
        {
          name: 'Admin',
          class_name: 'AdminPreview',
          emails: ['new_user_signup', 'new_account_pending', 'system_alert'],
          description: 'Administrative email notifications'
        },
        {
          name: 'PasswordsMailer',
          class_name: 'PasswordsMailerPreview',
          emails: ['reset'],
          description: 'Password reset email notifications'
        }
      ]
    end
  end
end