module Admin
  class ProductsController < BaseController
    before_action :set_product, only: [:show, :edit, :update, :destroy]

    def index
      @products = Product.includes(:account, :category, account: :company_profile)
                         .order(created_at: :desc)
      
      # Search functionality
      if params[:search].present?
        search_term = "%#{params[:search]}%"
        @products = @products.joins(account: :company_profile).where(
          "products.name LIKE ? OR products.description LIKE ? OR company_profiles.company_name LIKE ?",
          search_term, search_term, search_term
        )
      end
      
      # Filter by published status if specified
      @products = @products.where(published: params[:published]) if params[:published].present?
      
      
      # Filter by pricing model if specified  
      @products = @products.where(pricing_model: params[:pricing_model]) if params[:pricing_model].present?
      
      # Pagination
      @pagy, @products = pagy(@products)
    end

    def show
    end

    def new
      @product = Product.new
      @accounts = Account.includes(:company_profile).order(:name)
      @categories = Category.order(:name)
    end

    def create
      @product = Product.new(product_params)

      if @product.save
        redirect_to admin_product_path(@product), notice: 'Product was successfully created.'
      else
        @accounts = Account.includes(:company_profile).order(:name)
        @categories = Category.order(:name)
        render :new, status: :unprocessable_entity
      end
    end

    def edit
      @accounts = Account.includes(:company_profile).order(:name)
      @categories = Category.order(:name)
    end

    def update
      if @product.update(product_params)
        redirect_to admin_product_path(@product), notice: 'Product was successfully updated.'
      else
        @accounts = Account.includes(:company_profile).order(:name)
        @categories = Category.order(:name)
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @product.destroy!
      redirect_to admin_products_path, notice: 'Product was successfully deleted.'
    end

    private

    def set_product
      @product = Product.friendly.find(params[:id])
    end

    def product_params
      permitted_params = params.require(:product).permit(
        :name, :description, :account_id, :pricing_model, :published, :category_id
      )

      # Handle features parameter - convert from form input to JSON array
      if params[:product][:features_input].present?
        feature_names = params[:product][:features_input].split(',').map(&:strip).reject(&:blank?)
        permitted_params[:features] = feature_names.map { |name| { "name" => name } }
      else
        permitted_params[:features] = []
      end

      permitted_params
    end
  end
end