module Admin
  class CompaniesController < BaseController
    before_action :set_company, only: [:show, :edit, :update, :destroy]

    def index
      @companies = CompanyProfile.includes(:account)
                                 .order(created_at: :desc)
      
      # Search functionality
      if params[:search].present?
        search_term = "%#{params[:search]}%"
        @companies = @companies.where(
          "company_name LIKE ? OR website LIKE ? OR contact_email LIKE ?",
          search_term, search_term, search_term
        )
      end
      
      # Filter by published status if specified
      @companies = @companies.where(published: params[:published]) if params[:published].present?
      
      # Filter by company size if specified  
      @companies = @companies.where(company_size: params[:company_size]) if params[:company_size].present?
      
      # Pagination
      @pagy, @companies = pagy(@companies)
    end

    def show
    end

    def new
      @company = CompanyProfile.new
      @accounts = Account.order(:name)
    end

    def create
      @company = CompanyProfile.new(company_params)
      
      if @company.save
        redirect_to admin_company_path(@company), notice: 'Company was successfully created.'
      else
        @accounts = Account.order(:name)
        render :new, status: :unprocessable_entity
      end
    end

    def edit
      @accounts = Account.order(:name)
    end

    def update
      if @company.update(company_params)
        redirect_to admin_company_path(@company), notice: 'Company was successfully updated.'
      else
        @accounts = Account.order(:name)
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @company.destroy!
      redirect_to admin_companies_path, notice: 'Company was successfully deleted.'
    end

    private

    def set_company
      @company = CompanyProfile.friendly.find(params[:id])
    end

    def company_params
      params.require(:company_profile).permit(
        :company_name, :website, :contact_email, :contact_phone, :company_size, 
        :headquarters_location, :description, :published, :account_id,
        :logo
      )
    end
  end
end