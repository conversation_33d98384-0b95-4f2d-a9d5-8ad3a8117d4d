class Admin::CategoriesController < Admin::BaseController
  before_action :set_category, only: [:show, :edit, :update, :destroy]

  def index
    @categories = Category.alphabetical
    @categories = @categories.where("LOWER(name) LIKE LOWER(?)", "%#{params[:search]}%") if params[:search].present?
    @pagy, @categories = pagy(@categories)
  end

  def show
  end

  def new
    @category = Category.new
  end

  def create
    @category = Category.new(category_params)
    
    if @category.save
      redirect_to admin_categories_path, notice: 'Category was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @category.update(category_params)
      redirect_to admin_categories_path, notice: 'Category was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @category.products.any?
      redirect_to admin_categories_path, alert: 'Cannot delete category with associated products.'
    else
      @category.destroy
      redirect_to admin_categories_path, notice: 'Category was successfully deleted.'
    end
  end

  private

  def set_category
    @category = Category.friendly.find(params[:id])
  end

  def category_params
    params.require(:category).permit(:name)
  end
end