module Admin
  class HealthController < BaseController
    def show
      render json: {
        status: 'healthy',
        timestamp: Time.current.iso8601,
        version: Rails.application.class.module_parent_name.downcase,
        environment: Rails.env,
        checks: {
          database: database_check,
          jobs: jobs_check,
          storage: storage_check,
          memory: memory_check
        },
        metrics: {
          uptime: calculate_uptime,
          users: User.count,
          accounts: Account.count,
          products: Product.count,
          leads: Lead.count,
          sessions: Session.count
        }
      }
    end

    private

    def database_check
      begin
        ActiveRecord::Base.connection.execute("SELECT 1")
        { status: 'healthy', response_time: measure_time { User.first } }
      rescue => e
        { status: 'unhealthy', error: e.message }
      end
    end

    def jobs_check
      begin
        # Check SolidQueue job processing
        if defined?(SolidQueue)
          failed_jobs = SolidQueue::Job.failed.count rescue 0
          pending_jobs = SolidQueue::Job.pending.count rescue 0
          { 
            status: 'healthy', 
            failed_jobs: failed_jobs,
            pending_jobs: pending_jobs,
            response_time: measure_time { SolidQueue::Job.count }
          }
        else
          { status: 'not_configured' }
        end
      rescue => e
        { status: 'unhealthy', error: e.message }
      end
    end

    def storage_check
      begin
        test_file = Rails.root.join('tmp', 'health_check.txt')
        File.write(test_file, 'test')
        File.delete(test_file)
        { status: 'healthy', response_time: measure_time { File.write(test_file, 'test'); File.delete(test_file) } }
      rescue => e
        { status: 'unhealthy', error: e.message }
      end
    end

    def memory_check
      begin
        # Get memory usage information
        memory_info = `ps -o pid,ppid,pmem,rss,comm -p #{Process.pid}`.lines.last.split
        {
          status: 'healthy',
          pid: Process.pid,
          memory_percent: memory_info[2].to_f,
          memory_rss: "#{memory_info[3].to_i / 1024} MB"
        }
      rescue => e
        { status: 'unhealthy', error: e.message }
      end
    end

    def calculate_uptime
      boot_time = Rails.application.config.boot_time rescue Time.current
      uptime_seconds = Time.current - boot_time
      format_duration(uptime_seconds)
    end

    def format_duration(seconds)
      days = (seconds / 86400).floor
      hours = ((seconds % 86400) / 3600).floor
      minutes = ((seconds % 3600) / 60).floor
      
      if days > 0
        "#{days}d #{hours}h #{minutes}m"
      elsif hours > 0
        "#{hours}h #{minutes}m"
      else
        "#{minutes}m"
      end
    end

    def measure_time
      start_time = Time.current
      yield
      ((Time.current - start_time) * 1000).round(2) # milliseconds
    end
  end
end