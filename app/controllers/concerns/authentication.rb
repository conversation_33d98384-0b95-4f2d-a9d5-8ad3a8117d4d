module Authentication
  extend ActiveSupport::Concern

  included do
    before_action :resume_session
    before_action :require_authentication
    helper_method :authenticated?
  end

  class_methods do
    def allow_unauthenticated_access(**options)
      skip_before_action :require_authentication, **options
    end
  end

  private
    def authenticated?
      Current.session.present?
    end

    def require_authentication
      Current.session || request_authentication
    end

    def resume_session
      Current.session ||= find_session_by_cookie
    end

    def find_session_by_cookie
      Session.find_by(id: cookies.signed[:session_id]) if cookies.signed[:session_id]
    end

    def request_authentication
      session[:return_to_after_authenticating] = request.url
      redirect_to new_session_path
    end

    def after_authentication_url
      return_to = session.delete(:return_to_after_authenticating)
      return return_to if return_to
      
      # Redirect to appropriate dashboard based on user type
      if Current.user.admin?
        admin_root_url
      elsif Current.user.agency?
        agencies_root_url
      elsif Current.user.vendor?
        vendors_root_url
      else
        root_url
      end
    end

    def start_new_session_for(user)
      user.sessions.create!(user_agent: request.user_agent, ip_address: request.remote_ip).tap do |session|
        Current.session = session
        cookies.signed.permanent[:session_id] = { value: session.id, httponly: true, same_site: :lax }
        
        # Log successful login
        SecurityLogger.log_successful_login(user, request.remote_ip, request.user_agent)
      end
    end

    def terminate_session
      Current.session.destroy
      cookies.delete(:session_id)
    end
end
