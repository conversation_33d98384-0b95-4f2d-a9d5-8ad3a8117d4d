module ErrorHandling
  extend ActiveSupport::Concern

  included do
    # Handle common exceptions with user-friendly messages
    rescue_from ActiveRecord::RecordNotFound, with: :handle_not_found
    rescue_from ActionController::InvalidAuthenticityToken, with: :handle_csrf_error
    rescue_from ActionController::ParameterMissing, with: :handle_parameter_missing
    rescue_from ActiveRecord::RecordInvalid, with: :handle_record_invalid
    rescue_from Pundit::NotAuthorizedError, with: :handle_unauthorized if defined?(Pundit)
    
    # Handle generic errors in production
    if Rails.env.production?
      rescue_from StandardError, with: :handle_generic_error
    end
  end

  private

  def handle_not_found(exception)
    log_error(exception)
    
    respond_to do |format|
      format.html { redirect_to errors_not_found_path }
      format.json { render json: { error: "Resource not found" }, status: 404 }
    end
  end

  def handle_csrf_error(exception)
    SecurityLogger.log_csrf_failure(
      request.remote_ip,
      request.user_agent,
      request.referer
    )
    
    respond_to do |format|
      format.html do
        flash[:alert] = "Security token expired. Please try again."
        redirect_to request.referer || root_path
      end
      format.json { render json: { error: "Security token invalid" }, status: 422 }
    end
  end

  def handle_parameter_missing(exception)
    log_error(exception)
    
    respond_to do |format|
      format.html do
        flash[:alert] = "Required information is missing. Please check your input and try again."
        redirect_back(fallback_location: root_path)
      end
      format.json { render json: { error: "Required parameter missing: #{exception.param}" }, status: 422 }
    end
  end

  def handle_record_invalid(exception)
    log_error(exception)
    
    respond_to do |format|
      format.html do
        flash[:alert] = build_validation_error_message(exception.record)
        redirect_back(fallback_location: root_path)
      end
      format.json do 
        render json: { 
          error: "Validation failed", 
          details: exception.record.errors.full_messages 
        }, status: 422 
      end
    end
  end

  def handle_unauthorized(exception)
    SecurityLogger.log_unauthorized_access(
      request.path,
      request.remote_ip,
      request.user_agent,
      current_user&.id
    )
    
    respond_to do |format|
      format.html do
        flash[:alert] = "You don't have permission to access this resource."
        redirect_to root_path
      end
      format.json { render json: { error: "Access denied" }, status: 403 }
    end
  end

  def handle_generic_error(exception)
    log_error(exception, severity: :fatal)
    
    # Notify monitoring services in production
    if Rails.env.production?
      notify_error_tracking_service(exception)
    end
    
    respond_to do |format|
      format.html { redirect_to errors_internal_server_error_path }
      format.json { render json: { error: "An unexpected error occurred" }, status: 500 }
    end
  end

  def log_error(exception, severity: :error)
    error_details = {
      exception_class: exception.class.name,
      message: exception.message,
      backtrace: exception.backtrace&.first(10),
      request_path: request.path,
      request_method: request.method,
      user_id: current_user&.id,
      ip_address: request.remote_ip,
      user_agent: request.user_agent,
      params: safe_params_for_logging,
      timestamp: Time.current
    }

    case severity
    when :fatal
      Rails.logger.fatal(error_details.to_json)
    else
      Rails.logger.error(error_details.to_json)
    end
  end

  def build_validation_error_message(record)
    if record.errors.count == 1
      "#{record.errors.full_messages.first}."
    else
      "Please fix the following errors: #{record.errors.full_messages.join(', ')}."
    end
  end

  def safe_params_for_logging
    # Remove sensitive data from params before logging
    safe_params = params.except(:password, :password_confirmation, :authenticity_token)
    safe_params.to_h.deep_transform_values do |value|
      value.is_a?(String) && value.length > 500 ? "#{value[0..500]}..." : value
    end
  rescue
    # If params transformation fails, return empty hash
    {}
  end

  def notify_error_tracking_service(exception)
    # Placeholder for external error tracking service integration
    # Examples: Sentry, Rollbar, Honeybadger, etc.
    Rails.logger.info("Error notification sent to monitoring service: #{exception.class.name}")
  end
end