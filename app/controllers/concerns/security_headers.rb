module SecurityHeaders
  extend ActiveSupport::Concern

  included do
    before_action :set_security_headers
    before_action :sanitize_params
  end

  private

  def set_security_headers
    # Content Security Policy
    response.headers['Content-Security-Policy'] = content_security_policy
    
    # Other security headers
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
    
    # HSTS (only in production with HTTPS)
    if Rails.env.production? && request.ssl?
      response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'
    end
  end

  def content_security_policy
    # Base CSP policy
    policy_parts = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'", # Rails requires unsafe-inline for some features
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self'",
      "connect-src 'self'",
      "media-src 'self'",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'"
    ]
    
    policy_parts.join('; ')
  end

  def sanitize_params
    # Remove potentially dangerous parameters
    dangerous_keys = %w[
      __proto__ constructor prototype 
      eval function setTimeout setInterval
      innerHTML outerHTML
    ]
    
    dangerous_keys.each do |key|
      remove_dangerous_param(params, key)
    end

    # Sanitize string parameters
    sanitize_string_params(params)
  end

  def remove_dangerous_param(param_hash, key)
    return unless param_hash.is_a?(ActionController::Parameters) || param_hash.is_a?(Hash)
    
    param_hash.delete(key)
    param_hash.each_value do |value|
      if value.is_a?(ActionController::Parameters) || value.is_a?(Hash)
        remove_dangerous_param(value, key)
      elsif value.is_a?(Array)
        value.each { |item| remove_dangerous_param(item, key) if item.is_a?(Hash) }
      end
    end
  end

  def sanitize_string_params(param_hash)
    return unless param_hash.is_a?(ActionController::Parameters) || param_hash.is_a?(Hash)
    
    param_hash.each do |key, value|
      case value
      when String
        # Remove null bytes and control characters
        param_hash[key] = value.gsub(/\0/, '').gsub(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/, '')
        
        # Limit string length to prevent DoS
        param_hash[key] = param_hash[key].truncate(10000) if param_hash[key].length > 10000
        
      when ActionController::Parameters, Hash
        sanitize_string_params(value)
        
      when Array
        value.each_with_index do |item, index|
          if item.is_a?(String)
            value[index] = item.gsub(/\0/, '').gsub(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/, '')
            value[index] = value[index].truncate(10000) if value[index].length > 10000
          elsif item.is_a?(Hash)
            sanitize_string_params(item)
          end
        end
      end
    end
  end
end