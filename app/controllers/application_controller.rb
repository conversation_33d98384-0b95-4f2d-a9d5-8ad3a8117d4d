class ApplicationController < ActionController::Base
  include Authentication
  include SecurityHeaders
  include ErrorHandling
  include Pagy::Backend

  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  # allow_browser versions: :modern

  helper_method :current_user, :current_account, :current_user_account_admin?

  protected

  def current_user
    Current.user
  end

  def current_account
    return @current_account if defined?(@current_account)
    
    @current_account = if session[:account_id]
      current_user&.accounts&.find_by(id: session[:account_id])
    else
      current_user&.accounts&.first
    end
  end

  def set_current_account(account)
    session[:account_id] = account&.id
    @current_account = account
  end

  def require_account
    redirect_to root_path unless current_account
  end

  def require_vendor_account
    redirect_to root_path unless current_account&.vendor?
  end

  def require_agency_account
    redirect_to root_path unless current_account&.agency?
  end

  def require_approved_account
    redirect_to root_path unless current_account&.approved?
  end

  def require_admin
    redirect_to root_path unless current_user&.admin?
  end

  def current_user_account_admin?
    return false unless current_user && current_account
    
    account_user = current_account.account_users.find_by(user: current_user)
    account_user&.role == "admin" || current_account.owner == current_user || current_user.admin?
  end
end
