class RegistrationsController < ApplicationController
  allow_unauthenticated_access

  def new
    @user = User.new
  end

  def create
    @user = User.new(user_params)

    if @user.save
      # Create default account based on registration type
      account_type = params[:account_type] == "agency" ? "agency" : "vendor"
      account = Account.create!(
        owner: @user,
        account_type: account_type,
        name: "#{@user.full_name}'s #{account_type.capitalize} Account",
        status: "pending"
      )

      # Associate the user with the account
      account.account_users.create!(user: @user, role: "admin")

      # Update confirmation_sent_at timestamp
      account.update!(confirmation_sent_at: Time.current)

      # Send welcome email with confirmation link
      UserMailer.welcome(@user, account).deliver_later

      # Notify admins of new registration
      AdminMailer.new_user_signup(@user).deliver_later

      # Check if there's a pending invitation to accept
      if session[:invitation_token]
        invitation = TeamInvitation.find_by(token: session[:invitation_token])
        if invitation && invitation.status == "pending" && !invitation.expired? && invitation.email == @user.email_address
          if invitation.accept!(@user)
            session.delete(:invitation_token)
            redirect_to account_team_members_path, notice: "Welcome! You've successfully created your account and joined #{invitation.account.name}."
            return
          else
            session.delete(:invitation_token)
          end
        else
          session.delete(:invitation_token)
        end
      end

      redirect_to pending_path
    else
      render :new, status: :unprocessable_entity
    end
  end

  private

  def user_params
    params.require(:user).permit(:first_name, :last_name, :email_address, :password, :password_confirmation, :job_title, :company_name)
  end
end
