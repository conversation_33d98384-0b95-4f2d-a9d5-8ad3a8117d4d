class SitemapsController < ApplicationController
  def index
    @static_pages = [
      { url: root_url, changefreq: 'daily', priority: 1.0 },
      { url: marketplace_url, changefreq: 'daily', priority: 0.9 },
      { url: marketplace_products_url, changefreq: 'daily', priority: 0.8 },
      { url: marketplace_companies_url, changefreq: 'weekly', priority: 0.7 }
    ]

    @products = Product.published.includes(:account, :categories, :tags)
    @companies = CompanyProfile.published.includes(:account)

    respond_to do |format|
      format.xml
    end
  end

  def robots
    respond_to do |format|
      format.txt { render plain: robots_txt }
    end
  end

  private

  def robots_txt
    <<~ROBOTS
      User-agent: *
      Allow: /

      # Disallow admin areas
      Disallow: /admin/
      Disallow: /dashboard/

      # Sitemap
      Sitemap: #{sitemap_url}
    ROBOTS
  end
end