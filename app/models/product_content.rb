class ProductContent < ApplicationRecord
  self.table_name = "product_content"
  
  belongs_to :product
  has_one_attached :file

  validates :title, presence: true
  validates :description, presence: true
  validates :file, presence: true
  validate :file_validation

  scope :published, -> { where(published: true) }
  scope :by_position, -> { order(:position) }

  # Trigger video compression after video file is attached
  after_commit :schedule_video_compression, on: [:create, :update], if: :video_file_attached_and_changed?

  def published?
    published == true
  end

  def file_type
    return nil unless file.attached?
    
    case file.content_type
    when 'application/pdf'
      'pdf'
    when /^video\//
      'video'
    else
      'unknown'
    end
  end

  private

  def video_file_attached_and_changed?
    file.attached? && file_type == 'video' && saved_change_to_file?
  end

  def saved_change_to_file?
    # Check if file attachment changed in the last transaction
    file.attachment&.previously_changed? || 
    (file.attached? && created_at == updated_at)
  end

  def schedule_video_compression
    return unless file.attached?
    return unless file_type == 'video'
    
    VideoCompressionJob.perform_later(
      self.class.name,
      id,
      'file'
    )
  end

  def file_validation
    return unless file.attached?

    # Check file size (50 MB limit)
    if file.blob.byte_size > 50.megabytes
      errors.add(:file, "must be less than 50MB")
    end

    # Check content type (PDF and video files only)
    allowed_types = [
      'application/pdf',
      'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 
      'video/mov', 'video/wmv', 'video/flv', 'video/mkv'
    ]
    
    unless allowed_types.include?(file.blob.content_type)
      errors.add(:file, "must be a PDF or video file (MP4, WebM, OGG, AVI, MOV, WMV, FLV, MKV)")
    end
  end
end
