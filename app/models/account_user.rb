class AccountUser < ApplicationRecord
  belongs_to :account
  belongs_to :user

  validates :role, inclusion: { in: %w[admin member] }
  validates :user_id, uniqueness: { scope: :account_id }

  enum :role, { admin: 'admin', member: 'member' }

  scope :admins, -> { where(role: 'admin') }
  scope :members, -> { where(role: 'member') }

  def admin?
    role == 'admin'
  end

  def member?
    role == 'member'
  end
end
