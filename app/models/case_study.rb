class CaseStudy < ApplicationRecord
  belongs_to :product
  has_one_attached :upload

  validates :title, presence: true
  validates :description, presence: true
  validates :upload, presence: true
  validate :upload_validation

  scope :published, -> { where(published: true) }
  scope :by_position, -> { order(:position) }

  def published?
    published == true
  end

  private

  def upload_validation
    return unless upload.attached?

    # Check file size (20 MB limit)
    if upload.blob.byte_size > 20.megabytes
      errors.add(:upload, "must be less than 20MB")
    end

    # Check content type (PDF only)
    unless upload.blob.content_type == 'application/pdf'
      errors.add(:upload, "must be a PDF file")
    end
  end
end
