class TeamInvitation < ApplicationRecord
  belongs_to :account
  belongs_to :invited_by, class_name: 'User'

  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :role, inclusion: { in: %w[admin member] }
  validates :email, uniqueness: { scope: :account_id, message: "has already been invited to this account" }

  enum :status, { pending: 'pending', accepted: 'accepted', declined: 'declined', expired: 'expired' }

  before_create :generate_token_and_expiry

  scope :active, -> { where(status: 'pending').where('expires_at > ?', Time.current) }

  def expired?
    expires_at < Time.current
  end

  def accept!(user)
    return false if expired?
    
    ActiveRecord::Base.transaction do
      account.account_users.create!(
        user: user,
        role: role,
        joined_at: Time.current
      )
      update!(status: 'accepted')
    end
    true
  rescue ActiveRecord::RecordInvalid
    false
  end

  def decline!
    update!(status: 'declined')
  end

  private

  def generate_token_and_expiry
    begin
      self.token = SecureRandom.urlsafe_base64(32)
    end while TeamInvitation.exists?(token: token)
    self.expires_at = 7.days.from_now
  end
end
