class CompanyProfile < ApplicationRecord
  extend FriendlyId
  friendly_id :company_name, use: :slugged

  belongs_to :account
  has_one_attached :logo
  has_rich_text :description

  validates :company_name, presence: true
  validates :website, presence: true
  validates :contact_email, presence: true
  validates :account_id, uniqueness: true
  validate :logo_validation

  COMPANY_SIZES = [
    '1-10',
    '11-50', 
    '51-200',
    '201-500',
    '501-1000',
    '1000+'
  ].freeze

  scope :published, -> { where(published: true) }

  def published?
    published == true
  end

  def logo_thumbnail
    return unless logo.attached?
    logo.variant(resize_to_limit: [50, nil], format: :webp, saver: { quality: 85 })
  end

  def logo_small
    return unless logo.attached?
    logo.variant(resize_to_limit: [100, nil], format: :webp, saver: { quality: 85 })
  end

  def logo_medium
    return unless logo.attached?
    logo.variant(resize_to_limit: [200, nil], format: :webp, saver: { quality: 85 })
  end

  def logo_large
    return unless logo.attached?
    logo.variant(resize_to_limit: [400, nil], format: :webp, saver: { quality: 85 })
  end

  private

  def logo_validation
    return unless logo.attached?

    # Check file size
    if logo.blob.byte_size > 5.megabytes
      errors.add(:logo, "must be less than 5MB")
    end

    # Check content type
    allowed_types = %w[image/jpeg image/jpg image/png image/gif image/webp image/svg+xml]
    unless allowed_types.include?(logo.blob.content_type)
      errors.add(:logo, "must be a valid image format (JPEG, PNG, GIF, WebP, or SVG)")
    end
  end
end
