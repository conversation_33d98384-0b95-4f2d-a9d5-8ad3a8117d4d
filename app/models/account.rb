class Account < ApplicationRecord
  extend FriendlyId
  friendly_id :name, use: :slugged

  belongs_to :owner, class_name: "User"
  belongs_to :approved_by, class_name: "User", optional: true
  has_many :account_users, dependent: :destroy
  has_many :users, through: :account_users
  has_one :company_profile, dependent: :destroy
  has_many :products, dependent: :destroy
  has_many :team_invitations, dependent: :destroy
  has_many :leads, dependent: :destroy
  has_many :agency_leads, class_name: "Lead", foreign_key: "agency_account_id", dependent: :destroy

  validates :name, presence: true
  validates :account_type, inclusion: { in: %w[vendor agency admin] }
  validates :status, inclusion: { in: %w[pending approved rejected] }

  before_create :generate_confirmation_token

  enum :account_type, { vendor: "vendor", agency: "agency", admin: "admin" }
  enum :status, { pending: "pending", approved: "approved", rejected: "rejected" }

  scope :vendors, -> { where(account_type: "vendor") }
  scope :agencies, -> { where(account_type: "agency") }
  scope :admins, -> { where(account_type: "admin") }
  scope :approved, -> { where(status: "approved") }
  scope :pending_approval, -> { where(status: "pending") }
  scope :rejected, -> { where(status: "rejected") }

  def vendor?
    account_type == "vendor"
  end

  def agency?
    account_type == "agency"
  end

  def admin?
    account_type == "admin"
  end

  def approved?
    status == "approved"
  end

  def pending?
    status == "pending"
  end

  def rejected?
    status == "rejected"
  end

  def approve!(approved_by_user)
    update!(
      status: "approved",
      approved_at: Time.current,
      approved_by: approved_by_user
    )
  end

  def reject!(rejected_by_user)
    update!(
      status: "rejected",
      approved_at: nil,
      approved_by: rejected_by_user
    )
  end

  def confirmed?
    confirmed_at.present?
  end

  def send_confirmation_email
    self.update!(confirmation_sent_at: Time.current)
    UserMailer.welcome(self.owner, self).deliver_now
  end

  private

  def generate_confirmation_token
    self.confirmation_token = SecureRandom.urlsafe_base64(32)
  end
end
