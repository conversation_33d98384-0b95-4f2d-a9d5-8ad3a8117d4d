class Lead < ApplicationRecord
  belongs_to :product
  belongs_to :account
  belongs_to :user
  belongs_to :agency_account, class_name: 'Account', optional: true

  validates :status, inclusion: { in: %w[pending contacted qualified closed] }
  validates :contact_name, presence: true
  validates :contact_email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :contact_company, presence: true, on: :create

  enum :status, { pending: 'pending', contacted: 'contacted', qualified: 'qualified', closed: 'closed' }

  scope :recent, -> { order(created_at: :desc) }
end
