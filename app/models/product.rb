class Product < ApplicationRecord
  extend FriendlyId
  friendly_id :name, use: :slugged
  include PerformanceOptimizable

  belongs_to :account
  belongs_to :category, optional: true
  has_many :product_videos, -> { order(:position) }, dependent: :destroy
  has_many :product_content, -> { order(:position) }, dependent: :destroy
  has_many :case_studies, -> { order(:position) }, dependent: :destroy
  # Features are now stored as JSON array
  # has_many :product_features, dependent: :destroy
  # has_many :features, through: :product_features
  has_many :leads, dependent: :destroy
  has_rich_text :description

  validates :name, presence: true
  validates :account_id, presence: true
  validate :features_format

  # enum pricing_model: %w[monthly annual one_time custom free]

  scope :published, -> { where(published: true) }
  scope :featured, -> { where(featured: true) }
  scope :recent, -> { order(created_at: :desc) }
  scope :with_company, -> { includes(account: :company_profile) }

  def self.default_includes
    [:account, :category, { account: :company_profile }]
  end

  def self.search_optimized(query, limit: 20)
    return none if query.blank?
    
    where("LOWER(name) LIKE LOWER(?) OR LOWER(description) LIKE LOWER(?)", "%#{sanitize_sql_like(query)}%", "%#{sanitize_sql_like(query)}%")
      .includes(default_includes)
      .limit(limit)
  end


  def primary_video
    product_videos.by_position.first
  end

  def published?
    published == true
  end

  def self.pricing_models
    {
      'monthly' => 'Monthly',
      'annual' => 'Annual',
      'one_time' => 'One-time',
      'custom' => 'Custom',
      'free' => 'Free'
    }
  end

  # Helper methods for JSON features
  def feature_names
    return [] if features.blank?
    features.map { |f| f["name"] }.compact
  end

  def add_feature(name)
    return if name.blank?
    self.features = (features || []) + [{ "name" => name.strip }]
  end

  def remove_feature(name)
    return if name.blank? || features.blank?
    self.features = features.reject { |f| f["name"] == name }
  end

  def has_feature?(name)
    return false if name.blank? || features.blank?
    features.any? { |f| f["name"] == name }
  end

  private

  def features_format
    return if features.blank?

    unless features.is_a?(Array)
      errors.add(:features, "must be an array")
      return
    end

    features.each_with_index do |feature, index|
      unless feature.is_a?(Hash) && feature.key?("name")
        errors.add(:features, "feature at index #{index} must have a 'name' field")
      end

      if feature["name"].blank?
        errors.add(:features, "feature at index #{index} name cannot be blank")
      end
    end
  end
end
