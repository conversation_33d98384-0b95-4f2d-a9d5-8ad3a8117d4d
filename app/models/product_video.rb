class ProductVideo < ApplicationRecord
  belongs_to :product
  has_one_attached :video_file

  validates :title, presence: true
  validates :description, presence: true
  validates :video_file, presence: true
  validate :video_file_validation

  scope :published, -> { where(published: true) }
  scope :by_position, -> { order(:position) }

  # Trigger video compression after video file is attached
  after_commit :schedule_video_compression, on: [:create, :update], if: :video_file_attached_and_changed?

  def published?
    published == true
  end

  private

  def video_file_attached_and_changed?
    video_file.attached? && saved_change_to_video_file?
  end

  def saved_change_to_video_file?
    # Check if video_file attachment changed in the last transaction
    video_file.attachment&.previously_changed? || 
    (video_file.attached? && created_at == updated_at)
  end

  def schedule_video_compression
    return unless video_file.attached?
    return unless video_file.content_type&.start_with?('video/')
    
    VideoCompressionJob.perform_later(
      self.class.name,
      id,
      'video_file'
    )
  end

  def video_file_validation
    return unless video_file.attached?

    # Check file size (100 MB limit)
    if video_file.blob.byte_size > 100.megabytes
      errors.add(:video_file, "must be less than 100MB")
    end

    # Check content type
    allowed_types = %w[video/mp4 video/webm video/ogg video/avi video/mov video/wmv video/flv video/mkv]
    unless allowed_types.include?(video_file.blob.content_type)
      errors.add(:video_file, "must be a valid video format (MP4, WebM, OGG, AVI, MOV, WMV, FLV, MKV)")
    end
  end
end
