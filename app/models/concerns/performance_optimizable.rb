module PerformanceOptimizable
  extend ActiveSupport::Concern

  included do
    # Scope for frequently accessed records
    scope :with_includes, ->(includes) { includes(includes) }
    
    # Counter cache support
    after_create :increment_counters
    after_destroy :decrement_counters
  end

  class_methods do
    # Optimized finder methods
    def find_with_cache(id, expires_in: 1.hour)
      Rails.cache.fetch("#{self.name.underscore}_#{id}", expires_in: expires_in) do
        find(id)
      end
    end

    def search_optimized(query, limit: 20)
      return none if query.blank?
      
      # Use database-level search when available
      if respond_to?(:search)
        search(query).limit(limit)
      else
        # Fallback to basic text search
        where("LOWER(name) LIKE LOWER(?)", "%#{sanitize_sql_like(query)}%").limit(limit)
      end
    end

    # Batch loading for N+1 prevention
    def preload_associations
      includes(default_includes)
    end

    # Override in models to define default includes
    def default_includes
      []
    end
  end

  private

  def increment_counters
    # Override in models to define counter cache increments
  end

  def decrement_counters
    # Override in models to define counter cache decrements
  end
end