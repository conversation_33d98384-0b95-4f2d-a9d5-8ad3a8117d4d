class User < ApplicationRecord
  has_secure_password
  has_many :sessions, dependent: :destroy

  generates_token_for :password_reset, expires_in: 15.minutes

  has_one_attached :profile_photo

  def profile_photo_thumbnail
    return unless profile_photo.attached?
    profile_photo.variant(resize_to_fill: [50, 50], format: :webp, saver: { quality: 85 })
  end

  def profile_photo_avatar
    return unless profile_photo.attached?
    profile_photo.variant(resize_to_fill: [96, 96], format: :webp, saver: { quality: 85 })
  end

  def profile_photo_medium
    return unless profile_photo.attached?
    profile_photo.variant(resize_to_fill: [200, 200], format: :webp, saver: { quality: 85 })
  end

  def profile_photo_large
    return unless profile_photo.attached?
    profile_photo.variant(resize_to_fill: [400, 400], format: :webp, saver: { quality: 85 })
  end

  # Multi-tenancy associations
  has_many :owned_accounts, class_name: 'Account', foreign_key: 'owner_id', dependent: :destroy
  has_many :account_users, dependent: :destroy
  has_many :accounts, through: :account_users

  # Admin approval associations (for approving accounts)
  has_many :approved_accounts, class_name: 'Account', foreign_key: 'approved_by_id'

  normalizes :email_address, with: ->(e) { e.strip.downcase }

  validates :first_name, :last_name, presence: true
  validates :email_address, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :password, length: { minimum: 8 }, on: :create
  validates :password, length: { minimum: 8 }, on: :update, if: :password_digest_changed?
  validate :profile_photo_validation

  def full_name
    "#{first_name} #{last_name}".strip
  end

  def confirmed?
    confirmed_at.present?
  end

  def approved?
    # User is approved if they have at least one approved account
    accounts.any?(&:approved?)
  end

  def admin?
    super_admin?
  end

  def super_admin?
    super_admin == true
  end

  scope :super_admins, -> { where(super_admin: true) }
  scope :with_approved_accounts, -> { joins(:accounts).where(accounts: { status: 'approved' }).distinct }
  scope :with_pending_accounts, -> { joins(:accounts).where(accounts: { status: 'pending' }).distinct }

  def has_vendor_account?
    accounts.exists?(account_type: 'vendor')
  end

  def has_agency_account?
    accounts.exists?(account_type: 'agency')
  end

  # Role helper methods for navigation
  def vendor?
    has_vendor_account?
  end

  def agency?
    has_agency_account?
  end

  private

  def profile_photo_validation
    return unless profile_photo.attached?

    # Check file size
    if profile_photo.blob.byte_size > 5.megabytes
      errors.add(:profile_photo, "must be less than 5MB")
    end

    # Check content type
    allowed_types = %w[image/jpeg image/jpg image/png image/gif image/webp]
    unless allowed_types.include?(profile_photo.blob.content_type)
      errors.add(:profile_photo, "must be a valid image format (JPEG, PNG, GIF, or WebP)")
    end
  end
end
