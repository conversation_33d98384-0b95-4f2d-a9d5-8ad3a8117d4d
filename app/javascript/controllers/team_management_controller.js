import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="team-management"
export default class extends Controller {
  static targets = ["roleSelect", "confirmDialog"]

  connect() {
    // Add any initialization code here
  }

  confirmRoleChange(event) {
    const newRole = event.target.value
    const memberName = event.target.dataset.memberName
    
    if (newRole === "admin") {
      const confirmed = confirm(`Are you sure you want to give ${memberName} admin privileges? <PERSON><PERSON> can manage team members and modify company settings.`)
      if (!confirmed) {
        // Reset to previous value
        event.target.value = event.target.dataset.previousValue
        return false
      }
    }
    
    // Store the new value as previous for next time
    event.target.dataset.previousValue = newRole
    return true
  }

  confirmRemoval(event) {
    const memberName = event.target.dataset.memberName
    return confirm(`Are you sure you want to remove ${memberName} from the team? They will lose access to all company data and products.`)
  }

  showInvitationHelp() {
    // Could expand this to show a modal with more detailed help
    alert("Team invitations are sent via email and are valid for 7 days. If the person doesn't have an account, they'll be prompted to create one when they accept the invitation.")
  }
}