import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="delete-resource"
export default class extends Controller {
  static targets = ["modal", "cancelButton", "itemName"]
  static values = { 
    itemName: String,
    resourceType: String
  }

  connect() {
    // Set the item name in the modal if provided
    if (this.hasItemNameTarget && this.itemNameValue) {
      this.itemNameTarget.textContent = this.itemNameValue
    }
  }

  showModal() {
    this.modalTarget.classList.remove('hidden')
    document.body.classList.add('overflow-hidden')
  }

  hideModal() {
    this.modalTarget.classList.add('hidden')
    document.body.classList.remove('overflow-hidden')
  }

  // Handle clicking outside the modal
  clickOutside(event) {
    if (event.target === this.modalTarget || event.target.classList.contains('modal-backdrop')) {
      this.hideModal()
    }
  }
}