import { Controller } from "@hotwired/stimulus"
import Sortable from "sortablejs"

// Connects to data-controller="sortable"
export default class extends Controller {
  static values = { 
    url: String,
    handle: String
  }

  connect() {
    this.sortable = Sortable.create(this.element, {
      handle: this.handleValue || '.drag-handle',
      animation: 150,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      onEnd: this.updatePosition.bind(this)
    })
  }

  disconnect() {
    if (this.sortable) {
      this.sortable.destroy()
    }
  }

  updatePosition(event) {
    const itemId = event.item.dataset.itemId
    const newPosition = event.newIndex + 1 // 1-based position
    
    if (!this.urlValue || !itemId) {
      console.error('Missing URL or item ID for position update')
      return
    }

    // Build the URL with the item ID
    const url = this.urlValue.replace(':id', itemId)
    
    fetch(url, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({
        position: newPosition
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.status !== 'success') {
        alert('Error updating position: ' + (data.errors ? data.errors.join(', ') : 'Unknown error'))
        // Revert the sort
        this.sortable.sort(this.sortable.toArray())
      }
    })
    .catch(error => {
      console.error('Error:', error)
      alert('Error updating position')
      // Revert the sort
      this.sortable.sort(this.sortable.toArray())
    })
  }
}