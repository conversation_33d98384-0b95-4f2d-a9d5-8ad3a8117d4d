class TeamInvitationMailer < ApplicationMailer
  def invitation_email(team_invitation)
    @team_invitation = team_invitation
    @account = @team_invitation.account
    @invited_by = @team_invitation.invited_by
    @accept_url = accept_invitation_url(token: @team_invitation.token)
    @decline_url = decline_invitation_url(token: @team_invitation.token)

    mail(
      to: @team_invitation.email,
      subject: "You're invited to join #{@account.name} on Platia"
    )
  end
end
