class UserMailer < ApplicationMailer
  default from: "<EMAIL>"

  def welcome(user, account = nil)
    @user = user
    @account = account || user.owned_accounts.first
    @login_url = new_session_url

    if @account&.confirmation_token
      @confirmation_url = confirm_account_url(token: @account.confirmation_token)
      @needs_confirmation = true
    else
      @needs_confirmation = false
    end

    mail(
      to: @user.email_address,
      subject: "Welcome to Platia!"
    )
  end

  def account_approved(user)
    @user = user
    @dashboard_url = case @user.accounts.first&.account_type
    when "vendor"
                      dashboard_root_url
    when "agency"
                      agency_root_url
    else
                      root_url
    end

    mail(
      to: @user.email_address,
      subject: "Your Platia account has been approved!"
    )
  end

  def account_rejected(user, reason = nil)
    @user = user
    @reason = reason
    @contact_email = "<EMAIL>"

    mail(
      to: @user.email_address,
      subject: "Platia Account Status Update"
    )
  end

  def password_reset(user, token)
    @user = user
    @token = token
    @reset_url = edit_password_url(token: @token)

    mail(
      to: @user.email_address,
      subject: "Reset your Platia password"
    )
  end
end
