class LeadMailer < ApplicationMailer
  def new_lead_notification(lead)
    @lead = lead
    @product = @lead.product
    @vendor_account = @lead.account
    @agency_account = @lead.agency_account
    @vendor_user = @vendor_account.owner
    @dashboard_url = dashboard_leads_url

    mail(
      to: @vendor_user.email_address,
      subject: "New inquiry for #{@product.name} - Platia"
    )
  end

  def lead_status_update(lead)
    @lead = lead
    @product = @lead.product
    @vendor_account = @lead.account
    @agency_account = @lead.agency_account
    @agency_user = @agency_account.owner
    @interests_url = agency_interests_url

    mail(
      to: @agency_user.email_address,
      subject: "Update on your inquiry for #{@product.name}"
    )
  end

  def lead_response(lead, message)
    @lead = lead
    @product = @lead.product
    @vendor_account = @lead.account
    @agency_account = @lead.agency_account
    @agency_user = @agency_account.owner
    @message = message
    @vendor_company = @vendor_account.company_profile&.company_name || @vendor_account.name
    @interests_url = agency_interests_url

    mail(
      to: @agency_user.email_address,
      subject: "Response from #{@vendor_company} regarding #{@product.name}"
    )
  end
end