class AdminMailer < ApplicationMailer
  def new_user_signup(user)
    @user = user
    @admin_user_url = admin_user_url(@user)
    @admin_users_url = admin_users_url

    mail(
      to: admin_emails,
      subject: "New user registration requires approval - #{@user.full_name}"
    )
  end

  def new_account_pending(account)
    @account = account
    @user = @account.owner
    @admin_user_url = admin_user_url(@user)
    @admin_users_url = admin_users_url

    mail(
      to: admin_emails,
      subject: "New #{@account.account_type} account pending approval - #{@account.name}"
    )
  end

  def system_alert(subject, message, details = {})
    @message = message
    @details = details
    @timestamp = Time.current
    @health_url = admin_health_url

    mail(
      to: super_admin_emails,
      subject: "[SYSTEM ALERT] #{subject}"
    )
  end

  def weekly_report(start_date, end_date)
    @start_date = start_date
    @end_date = end_date
    @stats = calculate_weekly_stats(start_date, end_date)
    @admin_overview_url = admin_root_url

    mail(
      to: admin_emails,
      subject: "Weekly Platia Report - #{start_date.strftime('%B %d')} to #{end_date.strftime('%B %d, %Y')}"
    )
  end

  private

  def admin_emails
    User.where(super_admin: true).pluck(:email_address)
  end

  def super_admin_emails
    User.where(super_admin: true).pluck(:email_address)
  end

  def calculate_weekly_stats(start_date, end_date)
    {
      new_users: User.where(created_at: start_date..end_date).count,
      new_accounts: Account.where(created_at: start_date..end_date).count,
      new_products: Product.where(created_at: start_date..end_date).count,
      new_leads: Lead.where(created_at: start_date..end_date).count,
      pending_approvals: User.where(approved_at: nil).count,
      total_users: User.count,
      total_accounts: Account.count,
      active_products: Product.where(published: true).count
    }
  end
end