<% content_for :page_title, "Companies" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="mb-8">
  <div class="flex items-center justify-between">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 transition-colors">Browse Companies</h1>
    <div class="text-sm text-gray-500 dark:text-gray-400 transition-colors">
      <%= pluralize(@companies.count, 'company') %> found
    </div>
  </div>
</div>

<!-- Search and Filters -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8 transition-colors">
  <%= form_with url: marketplace_companies_path, method: :get, local: true do |form| %>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <%= form.label :search, "Search", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-colors" %>
        <%= form.text_field :search, 
            placeholder: "Company name, description...", 
            value: params[:search],
            class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors" %>
      </div>
      
      <div>
        <%= form.label :company_size, "Company Size", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-colors" %>
        <div class="mt-2 grid grid-cols-1">
          <%= form.select :company_size, 
              options_for_select([['All sizes', '']] + @company_sizes.map { |size| [size, size] }, params[:company_size]),
              {},
              class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-800 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm transition-colors" %>
          <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
            <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
      
      <div class="flex items-end">
        <%= form.submit "Search", class: "w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" %>
      </div>
    </div>
  <% end %>
</div>

<!-- Companies List -->
<% if @companies.any? %>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <% @companies.each do |company| %>
      <div class="group">
        <%= link_to marketplace_company_path(company), 
            class: "block bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden group-hover:border-blue-300 dark:group-hover:border-blue-600" do %>
          <div class="p-6">
            <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-xl mx-auto mb-4 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors overflow-hidden">
              <% if company.logo.attached? %>
                <%= image_tag company.logo_small, 
                    class: "max-w-full max-h-full object-contain", 
                    alt: "#{company.company_name} logo" %>
              <% else %>
                <span class="text-blue-600 dark:text-blue-400 font-bold text-xl transition-colors">
                  <%= company.company_name.first.upcase %>
                </span>
              <% end %>
            </div>
            <h3 class="font-semibold text-gray-900 dark:text-gray-100 text-center group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-2">
              <%= company.company_name %>
            </h3>
            <% if company.company_size.present? %>
              <p class="text-center text-sm text-gray-600 dark:text-gray-400 mb-2 transition-colors">
                <%= company.company_size %> employees
              </p>
            <% end %>
            <% if company.description.present? %>
              <p class="text-sm text-gray-600 dark:text-gray-400 text-center transition-colors">
                <%= truncate(strip_tags(company.description), length: 100) %>
              </p>
            <% end %>
            <% if company.account.products.published.any? %>
              <div class="mt-4 text-center">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition-colors">
                  <%= pluralize(company.account.products.published.count, 'product') %>
                </span>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>
<% else %>
  <div class="text-center py-12">
    <div class="max-w-md mx-auto">
      <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-xl mx-auto mb-4 flex items-center justify-center transition-colors">
        <svg class="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 transition-colors">No companies found</h3>
      <p class="text-gray-500 dark:text-gray-400 mb-6 transition-colors">
        <% if request.query_parameters.values.any?(&:present?) %>
          Try adjusting your search criteria or filters.
        <% else %>
          There are no companies available at the moment.
        <% end %>
      </p>
      <% if request.query_parameters.values.any?(&:present?) %>
        <%= link_to "Clear all filters", marketplace_companies_path, 
            class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" %>
      <% end %>
    </div>
  </div>
<% end %>
</div>