<% content_for :page_title, "Marketplace" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Hero Search Section -->
  <div class="bg-gradient-to-r from-blue-600 to-blue-800 dark:from-blue-700 dark:to-blue-900 rounded-xl shadow-xl mb-8">
    <div class="px-8 py-12 text-center">
    <h1 class="text-4xl font-bold text-white mb-4">
      Discover Government Technology Solutions
    </h1>
    <p class="text-xl text-blue-100 dark:text-blue-200 mb-8 max-w-3xl mx-auto">
      Find the perfect technology partners and products to modernize your government operations
    </p>
    
    <%= form_with url: marketplace_products_path, method: :get, local: true, class: "max-w-4xl mx-auto" do |form| %>
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <%= form.text_field :search, 
              placeholder: "Search products, companies, or solutions...", 
              value: params[:search],
              class: "w-full px-6 py-4 text-lg bg-white dark:bg-gray-800 dark:text-white border-0 rounded-xl shadow-lg focus:ring-4 focus:ring-blue-300 focus:outline-none" %>
        </div>
        <div>
          <%= form.submit "Search", 
              class: "w-full sm:w-auto px-8 py-4 bg-white dark:bg-gray-100 text-blue-600 dark:text-blue-700 font-semibold rounded-xl shadow-lg hover:bg-gray-50 dark:hover:bg-gray-200 transition-colors focus:outline-none focus:ring-4 focus:ring-blue-300" %>
        </div>
      </div>
    <% end %>
  </div>
</div>

<!-- Categories Section -->
<% cache(['categories', Category.maximum(:updated_at)], expires_in: 1.hour) do %>
  <div class="mb-12">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">Browse by Category</h2>
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
      <% @categories.each do |category| %>
        <div class="group">
          <%= link_to marketplace_products_path(category_id: category.id), 
              class: "block bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700 p-4 text-center group-hover:border-blue-300 dark:group-hover:border-blue-500" do %>
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg mx-auto mb-3 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
              <span class="text-blue-600 dark:text-blue-400 font-bold text-lg">
                <%= category.name.first.upcase %>
              </span>
            </div>
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              <%= category.name %>
            </h3>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>

<!-- Featured Products -->
<% if @featured_products.any? %>
  <% cache(['featured_products', @featured_products.maximum(:updated_at), current_account&.account_type], expires_in: 30.minutes) do %>
    <div class="mb-12">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Featured Products</h2>
        <%= link_to "View all products", marketplace_products_path, 
            class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium" %>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <% @featured_products.each do |product| %>
          <% cache(['product_card', product, product.account.company_profile&.updated_at], expires_in: 1.hour) do %>
            <div class="group">
              <%= link_to marketplace_product_path(product), 
                  class: "block bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden group-hover:border-blue-300 dark:group-hover:border-blue-500" do %>
                <div class="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-700">
                  <!-- Product image placeholder or actual image -->
                  <div class="flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800">
                    <span class="text-blue-600 dark:text-blue-400 text-2xl font-bold">
                      <%= product.name.first.upcase %>
                    </span>
                  </div>
                </div>
                <div class="p-4">
                  <h3 class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-1">
                    <%= truncate(product.name, length: 50) %>
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    by <%= product.account.company_profile&.company_name || product.account.name %>
                  </p>
                  <% if product.description.present? %>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      <%= truncate(strip_tags(product.description), length: 80) %>
                    </p>
                  <% end %>
                  <div class="flex items-center justify-between">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                      <%= product.pricing_model.humanize %>
                    </span>
                    <% if product.category.present? %>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        <%= product.category.name %>
                      </span>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
  <% end %>
<% end %>

<!-- Recently Added Products -->
<% if @recently_added_products.any? %>
  <div class="mb-12">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Recently Added Products</h2>
      <%= link_to "View all", marketplace_products_path(sort: 'newest'), 
          class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium" %>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <% @recently_added_products.each do |product| %>
        <div class="group">
          <%= link_to marketplace_product_path(product), 
              class: "block bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden group-hover:border-green-300 dark:group-hover:border-green-500" do %>
            <div class="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-700 relative">
              <div class="flex items-center justify-center bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900 dark:to-green-800">
                <span class="text-green-600 dark:text-green-400 text-2xl font-bold">
                  <%= product.name.first.upcase %>
                </span>
              </div>
              <div class="absolute top-2 right-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                  New
                </span>
              </div>
            </div>
            <div class="p-4">
              <h3 class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors mb-1">
                <%= truncate(product.name, length: 50) %>
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                by <%= product.account.company_profile&.company_name || product.account.name %>
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">
                Added <%= time_ago_in_words(product.created_at) %> ago
              </p>
              <% if product.description.present? %>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  <%= truncate(strip_tags(product.description), length: 80) %>
                </p>
              <% end %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>

<!-- Recently Added Case Studies -->
<% if @recently_added_case_studies.any? %>
  <div class="mb-12">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Recently Added Case Studies</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @recently_added_case_studies.each do |case_study| %>
        <div class="group">
          <%= link_to marketplace_product_path(case_study.product), 
              class: "block bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden group-hover:border-purple-300 dark:group-hover:border-purple-500" do %>
            <div class="p-6">
              <div class="flex items-start justify-between mb-3">
                <h3 class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                  <%= truncate(case_study.title, length: 60) %>
                </h3>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 ml-2 flex-shrink-0">
                  Case Study
                </span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                for <%= case_study.product.name %>
              </p>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                by <%= case_study.product.account.company_profile&.company_name || case_study.product.account.name %>
              </p>
              <% if case_study.description.present? %>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  <%= truncate(strip_tags(case_study.description), length: 120) %>
                </p>
              <% end %>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Added <%= time_ago_in_words(case_study.created_at) %> ago
              </p>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>

<!-- Featured Companies -->
<% if @featured_companies.any? %>
  <div class="mb-12">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Featured Companies</h2>
      <%= link_to "View all companies", marketplace_companies_path, 
          class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium" %>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <% @featured_companies.each do |company| %>
        <div class="group">
          <%= link_to marketplace_company_path(company), 
              class: "block bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden group-hover:border-blue-300 dark:group-hover:border-blue-500" do %>
            <div class="p-6">
              <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-xl mx-auto mb-4 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
                <span class="text-blue-600 dark:text-blue-400 font-bold text-xl">
                  <%= company.company_name.first.upcase %>
                </span>
              </div>
              <h3 class="font-semibold text-gray-900 dark:text-gray-100 text-center group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-2">
                <%= company.company_name %>
              </h3>
              <% if company.company_size.present? %>
                <p class="text-center text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <%= company.company_size %> employees
                </p>
              <% end %>
              <% if company.description.present? %>
                <p class="text-sm text-gray-600 dark:text-gray-400 text-center">
                  <%= truncate(strip_tags(company.description), length: 80) %>
                </p>
              <% end %>
              <% if company.account.products.published.any? %>
                <div class="mt-3 text-center">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                    <%= pluralize(company.account.products.published.count, 'product') %>
                  </span>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>
</div>