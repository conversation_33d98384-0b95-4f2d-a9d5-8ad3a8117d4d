<% content_for :page_title, "#{@case_study.title} - #{@product.name}" %>
<% content_for :meta_description, "#{@case_study.title} from #{@product.name} by #{@company&.company_name || @product.account.name}. #{strip_tags(@case_study.description.to_s).truncate(120)}" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="lg:grid lg:grid-cols-4 lg:gap-8">
    
    <!-- Main Content -->
    <div class="lg:col-span-3">
      <!-- Breadcrumb -->
      <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2">
          <li>
            <%= link_to marketplace_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              Marketplace
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li>
            <%= link_to marketplace_products_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              Products
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li>
            <%= link_to marketplace_product_path(@product), class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              <%= truncate(@product.name, length: 30) %>
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li class="text-gray-500 dark:text-gray-400">
            <%= truncate(@case_study.title, length: 40) %>
          </li>
        </ol>
      </nav>

      <!-- Case Study Header -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
        <div class="px-6 py-8">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-colors">
                <%= @case_study.title %>
              </h1>
              <p class="text-lg text-gray-600 dark:text-gray-400 mb-4 transition-colors">
                From <%= link_to @product.name, marketplace_product_path(@product), 
                          class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors" %>
                by <% if @company %>
                  <%= link_to @company.company_name, 
                            marketplace_company_path(@company), 
                            class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors" %>
                <% else %>
                  <%= @product.account.name %>
                <% end %>
              </p>
              
              <% if @case_study.description.present? %>
                <div class="prose max-w-none dark:prose-invert">
                  <%= simple_format(@case_study.description) %>
                </div>
              <% end %>
            </div>
            
            <% if @case_study.upload.attached? %>
              <div class="ml-6">
                <%= link_to url_for(@case_study.upload), 
                    download: @case_study.upload.filename,
                    class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download PDF
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- PDF Viewer -->
      <% if @case_study.upload.attached? %>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Case Study PDF</h2>
            <div class="aspect-[8.5/11] bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
              <iframe 
                src="<%= url_for(@case_study.upload) %>#toolbar=0&navpanes=0&scrollbar=0"
                class="w-full h-full border-0"
                title="<%= @case_study.title %> PDF">
              </iframe>
            </div>
            
            <div class="mt-4 flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                  <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                  </svg>
                  PDF • <%= number_to_human_size(@case_study.upload.byte_size) %>
                </span>
              </div>
              <div class="flex space-x-2">
                <%= link_to 'Open in New Tab', url_for(@case_study.upload), 
                    target: '_blank',
                    class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
              </div>
            </div>
          </div>
        </div>
      <% else %>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <div class="aspect-[8.5/11] bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No PDF available</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">PDF file not available for this case study.</p>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Case Study Details -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors">
        <div class="px-6 py-8">
          <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Case Study Details</h2>
          <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <% if @case_study.upload.attached? %>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Name</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @case_study.upload.filename %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Size</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= number_to_human_size(@case_study.upload.byte_size) %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Content Type</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @case_study.upload.content_type %></dd>
              </div>
            <% end %>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @case_study.created_at.strftime("%B %-d, %Y") %></dd>
            </div>
          </dl>
        </div>
      </div>

    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1 mt-8 lg:mt-0">
      <div class="sticky top-6">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">About This Product</h3>
          
          <div class="space-y-4">
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Product</p>
              <p class="text-sm text-gray-900 dark:text-gray-100">
                <%= link_to @product.name, marketplace_product_path(@product), 
                    class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" %>
              </p>
            </div>
            
            <% if @product.category %>
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Category</p>
                <p class="text-sm text-gray-900 dark:text-gray-100"><%= @product.category.name %></p>
              </div>
            <% end %>
            
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Vendor</p>
              <p class="text-sm text-gray-900 dark:text-gray-100">
                <% if @company %>
                  <%= link_to @company.company_name, marketplace_company_path(@company), 
                      class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" %>
                <% else %>
                  <%= @product.account.name %>
                <% end %>
              </p>
            </div>
            
            <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
              <%= link_to 'Back to Product', marketplace_product_path(@product), 
                  class: 'w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            </div>
          </div>
        </div>
      </div>
    </div>
    
  </div>
</div>