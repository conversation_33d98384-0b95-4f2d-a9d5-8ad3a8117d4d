<% content_for :page_title, @product.name %>
<% content_for :meta_description, "#{@product.name} by #{@company&.company_name || @product.account.name}. #{strip_tags(@product.description.to_s).truncate(120)}" %>
<% content_for :meta_keywords, [@product.category&.name, @product.feature_names, 'government technology', 'govtech solution'].flatten.compact %>

<% content_for :structured_data do %>
  <%= render_structured_data(structured_data_product(@product)) %>
<% end %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="lg:grid lg:grid-cols-4 lg:gap-8">
  
    <!-- Main Content -->
    <div class="lg:col-span-3">
      <!-- Breadcrumb -->
      <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2">
          <li>
            <%= link_to marketplace_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              Marketplace
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600 transition-colors" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li>
            <%= link_to marketplace_products_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              Products
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600 transition-colors" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li class="text-gray-500 dark:text-gray-400 transition-colors">
            <%= truncate(@product.name, length: 50) %>
          </li>
        </ol>
      </nav>

      <!-- Product Header -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
        <div class="px-6 py-8">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-colors">
                <%= @product.name %>
              </h1>
              <p class="text-lg text-gray-600 dark:text-gray-400 mb-4 transition-colors">
                by <% if @company %>
                  <%= link_to @company.company_name, 
                            marketplace_company_path(@company), 
                            class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors" %>
                <% else %>
                  <%= @product.account.name %>
                <% end %>
              </p>
              
              <!-- Category and Features -->
              <div class="flex flex-wrap gap-2 mb-6">
                <% if @product.category %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition-colors">
                    <%= @product.category.name %>
                  </span>
                <% end %>
                <% @product.feature_names.each do |feature_name| %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors">
                    <%= feature_name %>
                  </span>
                <% end %>
              </div>
            </div>
            
            <div class="ml-6">
              <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition-colors">
                <%= @product.pricing_model.humanize %> Pricing
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Demo Video Section -->
      <% if @demo_video %>
        <!-- Section Heading -->
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Product Demo</h2>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <% if can_view_product_content?(@product) %>
              <div class="aspect-w-16 aspect-h-9 bg-gray-900 rounded-lg overflow-hidden">
                <% if @demo_video.video_file.attached? %>
                  <video 
                    controls 
                    class="w-full h-full object-cover"
                    preload="metadata">
                    <source src="<%= url_for(@demo_video.video_file) %>" type="<%= @demo_video.video_file.content_type %>">
                    Your browser does not support the video tag.
                  </video>
                <% else %>
                  <div class="flex items-center justify-center text-white">
                    <div class="text-center">
                      <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                      </svg>
                      <p class="text-lg">Demo Video</p>
                      <% if @demo_video.title.present? %>
                        <p class="text-sm opacity-75"><%= @demo_video.title %></p>
                      <% end %>
                    </div>
                  </div>
                <% end %>
              </div>
              <% if @demo_video.description.present? %>
                <div class="mt-4">
                  <p class="text-gray-600 dark:text-gray-400 transition-colors"><%= @demo_video.description %></p>
                </div>
              <% end %>
            <% else %>
              <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Access Restricted</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Only approved government agencies and departments can view this content.</p>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- Product Description -->
      <% if @product.description.present? %>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">About This Product</h2>
            <div class="prose max-w-none dark:prose-invert">
              <%= simple_format(@product.description) %>
            </div>
          </div>
        </div>
      <% end %>


      <!-- Product Videos Section -->
      <% if @product_videos.any? %>
        <!-- Section Heading -->
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Product Videos</h2>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <% if can_view_product_content?(@product) %>
              <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                  <thead class="bg-gray-50 dark:bg-gray-900/50">
                    <tr>
                      <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Video</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Description</th>
                      <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                        <span class="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                    <% @product_videos.each do |video| %>
                      <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                          <div class="flex items-center">
                            <div class="h-10 w-10 flex-shrink-0">
                              <div class="h-10 w-10 rounded-lg bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center">
                                <svg class="h-5 w-5 text-indigo-600 dark:text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                </svg>
                              </div>
                            </div>
                            <div class="ml-4">
                              <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                <%= video.title %>
                              </div>
                              <% if video.video_file.attached? %>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                  <%= number_to_human_size(video.video_file.byte_size) %>
                                </div>
                              <% end %>
                            </div>
                          </div>
                        </td>
                        <td class="px-3 py-4 text-sm text-gray-600 dark:text-gray-400">
                          <% if video.description.present? %>
                            <%= truncate(strip_tags(video.description), length: 100) %>
                          <% else %>
                            <span class="text-gray-400 dark:text-gray-500 italic">No description</span>
                          <% end %>
                        </td>
                        <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                          <%= link_to 'View', marketplace_product_video_path(@product, video), 
                              class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 font-medium transition-colors' %>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% else %>
              <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Access Restricted</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Only approved government agencies and departments can view this content.</p>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- Case Studies Section -->
      <% if @case_studies.any? %>
        <!-- Section Heading -->
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Case Studies</h2>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <% if can_view_product_content?(@product) %>
              <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                  <thead class="bg-gray-50 dark:bg-gray-900/50">
                    <tr>
                      <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Case Study</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Description</th>
                      <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                        <span class="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                    <% @case_studies.each do |case_study| %>
                      <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                          <div class="flex items-center">
                            <div class="h-10 w-10 flex-shrink-0">
                              <div class="h-10 w-10 rounded-lg bg-red-100 dark:bg-red-900/50 flex items-center justify-center">
                                <svg class="h-5 w-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                                </svg>
                              </div>
                            </div>
                            <div class="ml-4">
                              <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                <%= case_study.title %>
                              </div>
                              <% if case_study.upload.attached? %>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                  PDF • <%= number_to_human_size(case_study.upload.byte_size) %>
                                </div>
                              <% end %>
                            </div>
                          </div>
                        </td>
                        <td class="px-3 py-4 text-sm text-gray-600 dark:text-gray-400">
                          <% if case_study.description.present? %>
                            <%= truncate(strip_tags(case_study.description), length: 100) %>
                          <% else %>
                            <span class="text-gray-400 dark:text-gray-500 italic">No description</span>
                          <% end %>
                        </td>
                        <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                          <%= link_to 'View', marketplace_case_study_path(@product, case_study), 
                              class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 font-medium transition-colors' %>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% else %>
              <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Access Restricted</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Only approved government agencies and departments can view this content.</p>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- Product Content Section -->
      <% if @product_content.any? %>
        <!-- Section Heading -->
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Product Content</h2>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <% if can_view_product_content?(@product) %>
              <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                  <thead class="bg-gray-50 dark:bg-gray-900/50">
                    <tr>
                      <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Content</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Type</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Description</th>
                      <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                        <span class="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                    <% @product_content.each do |content| %>
                      <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                          <div class="flex items-center">
                            <div class="h-10 w-10 flex-shrink-0">
                              <div class="h-10 w-10 rounded-lg <%= content.file_type == 'pdf' ? 'bg-red-100 dark:bg-red-900/50' : 'bg-purple-100 dark:bg-purple-900/50' %> flex items-center justify-center">
                                <% if content.file_type == 'pdf' %>
                                  <svg class="h-5 w-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                                  </svg>
                                <% else %>
                                  <svg class="h-5 w-5 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                                  </svg>
                                <% end %>
                              </div>
                            </div>
                            <div class="ml-4">
                              <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                <%= content.title %>
                              </div>
                              <% if content.file.attached? %>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                  <%= number_to_human_size(content.file.byte_size) %>
                                </div>
                              <% end %>
                            </div>
                          </div>
                        </td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm">
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= content.file_type == 'pdf' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' %>">
                            <%= content.file_type&.upcase %>
                          </span>
                        </td>
                        <td class="px-3 py-4 text-sm text-gray-600 dark:text-gray-400">
                          <% if content.description.present? %>
                            <%= truncate(strip_tags(content.description), length: 100) %>
                          <% else %>
                            <span class="text-gray-400 dark:text-gray-500 italic">No description</span>
                          <% end %>
                        </td>
                        <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                          <%= link_to 'View', marketplace_product_content_path(@product, content), 
                              class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 font-medium transition-colors' %>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% else %>
              <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Access Restricted</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Only approved government agencies and departments can view this content.</p>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- Related Products -->
      <% if @related_products.any? %>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors">
          <div class="px-6 py-8">
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">More from <%= @company&.company_name || @product.account.name %></h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <% @related_products.each do |product| %>
                <div class="group">
                  <%= link_to marketplace_product_path(product), 
                      class: "block border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md transition-all" do %>
                    <h3 class="font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 mb-2 transition-colors">
                      <%= truncate(product.name, length: 40) %>
                    </h3>
                    <% if product.description.present? %>
                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-2 transition-colors">
                        <%= truncate(strip_tags(product.description), length: 80) %>
                      </p>
                    <% end %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition-colors">
                      <%= product.pricing_model.humanize %>
                    </span>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

    </div>

    <!-- Sidebar - Ping Vendor -->
    <div class="lg:col-span-1">
      <div class="sticky top-6">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors">
          <div class="text-center mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Have questions, want more info or need a personalized demo?
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 transition-colors">
              Connect directly with the vendor to learn more about this solution.
            </p>
          </div>

          <% if @existing_interest %>
            <!-- Already expressed interest -->
            <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4 mb-4 transition-colors">
              <div class="flex">
                <svg class="w-5 h-5 text-green-400 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <div>
                  <h4 class="text-sm font-medium text-green-800 dark:text-green-200 transition-colors">Interest Submitted</h4>
                  <p class="text-sm text-green-700 dark:text-green-300 mt-1 transition-colors">
                    You expressed interest on <%= @existing_interest.created_at.strftime("%B %-d, %Y") %>. 
                    The vendor will contact you soon.
                  </p>
                </div>
              </div>
            </div>
            
          <% else %>
            <!-- Interest form -->
            <% if current_user %>
              <%= form_with model: [@product, @lead], url: agencies_product_interests_path(@product), local: true, class: "space-y-4" do |form| %>
                <div>
                  <%= form.label :contact_name, "Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
                  <%= form.text_field :contact_name, 
                      value: current_user&.full_name,
                      class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
                </div>

                <div>
                  <%= form.label :contact_email, "Email", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
                  <%= form.email_field :contact_email, 
                      value: current_user&.email_address,
                      class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
                </div>

                <div>
                  <%= form.label :contact_company, "Agency/Department", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
                  <%= form.text_field :contact_company, 
                      value: current_user&.accounts&.first&.name,
                      class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
                </div>

                <div>
                  <%= form.label :contact_phone, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
                    Phone <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
                  <% end %>
                  <%= form.telephone_field :contact_phone, 
                      value: current_user&.phone,
                      class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
                </div>

                <div>
                  <%= form.label :timeline, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
                    Timeline <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
                  <% end %>
                  <div class="mt-2 grid grid-cols-1">
                    <%= form.select :timeline, 
                        options_for_select([
                          ['Immediate (1-30 days)', 'immediate'],
                          ['Short term (1-3 months)', 'short_term'],
                          ['Medium term (3-6 months)', 'medium_term'],
                          ['Long term (6+ months)', 'long_term'],
                          ['Just exploring', 'exploring']
                        ]),
                        { prompt: 'Select timeline' },
                        { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm" } %>
                    <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                      <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </div>

                <div>
                  <%= form.label :budget_range, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
                    Budget Range <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
                  <% end %>
                  <div class="mt-2 grid grid-cols-1">
                    <%= form.select :budget_range, 
                        options_for_select([
                          ['Under $10K', 'under_10k'],
                          ['$10K - $50K', '10k_50k'],
                          ['$50K - $100K', '50k_100k'],
                          ['$100K - $500K', '100k_500k'],
                          ['$500K+', '500k_plus']
                        ]),
                        { prompt: 'Select budget range' },
                        { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm" } %>
                    <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                      <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </div>

                <div>
                  <%= form.label :message, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
                    Message <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
                  <% end %>
                  <%= form.text_area :message, 
                      rows: 3,
                      placeholder: "Tell us about your specific needs or questions...",
                      class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
                </div>

                <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors">
                  Ping Vendor
                </button>
              <% end %>
            <% else %>
              <!-- Form for unauthenticated users -->
              <%= form_with model: [@product, @lead], url: agencies_product_interests_path(@product), local: true, class: "space-y-4" do |form| %>
                <div>
                  <%= form.label :contact_name, "Your Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
                  <%= form.text_field :contact_name, 
                      class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
                </div>

                <div>
                  <%= form.label :contact_email, "Email", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
                  <%= form.email_field :contact_email, 
                      class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
                </div>

                <div>
                  <%= form.label :contact_company, "Agency/Department", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
                  <%= form.text_field :contact_company, 
                      class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
                </div>

                <div>
                  <%= form.label :contact_phone, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
                    Phone <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
                  <% end %>
                  <%= form.telephone_field :contact_phone, 
                      class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
                </div>

                <div>
                  <%= form.label :timeline, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
                    Timeline <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
                  <% end %>
                  <div class="mt-2 grid grid-cols-1">
                    <%= form.select :timeline, 
                        options_for_select([
                          ['Immediate (1-30 days)', 'immediate'],
                          ['Short term (1-3 months)', 'short_term'],
                          ['Medium term (3-6 months)', 'medium_term'],
                          ['Long term (6+ months)', 'long_term'],
                          ['Just exploring', 'exploring']
                        ]),
                        { prompt: 'Select timeline' },
                        { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm border-gray-300 dark:border-gray-600 transition-colors" } %>
                    <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                      <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </div>

                <div>
                  <%= form.label :budget_range, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
                    Budget Range <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
                  <% end %>
                  <div class="mt-2 grid grid-cols-1">
                    <%= form.select :budget_range, 
                        options_for_select([
                          ['Under $10K', 'under_10k'],
                          ['$10K - $50K', '10k_50k'],
                          ['$50K - $100K', '50k_100k'],
                          ['$100K - $500K', '100k_500k'],
                          ['$500K+', '500k_plus']
                        ]),
                        { prompt: 'Select budget range' },
                        { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm border-gray-300 dark:border-gray-600 transition-colors" } %>
                    <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                      <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </div>

                <div>
                  <%= form.label :message, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
                    Message <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
                  <% end %>
                  <%= form.text_area :message, 
                      rows: 3,
                      placeholder: "Tell us about your specific needs or questions...",
                      class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
                </div>

                <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors">
                  Ping Vendor
                </button>
              <% end %>
            <% end %>
          <% end %>

          <!-- Company Info -->
          <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 transition-colors">
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3 transition-colors">About the Vendor</h4>
            <% if @company %>
              <div class="text-sm text-gray-600 dark:text-gray-400 space-y-2 transition-colors">
                <p><strong><%= @company.company_name %></strong></p>
                <% if @company.company_size.present? %>
                  <p><%= @company.company_size %> employees</p>
                <% end %>
                <% if @company.headquarters_location.present? %>
                  <p><%= @company.headquarters_location %></p>
                <% end %>
                <% if @company.website.present? %>
                  <p>
                    <%= link_to @company.website, @company.website, 
                        target: "_blank", 
                        class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" %>
                  </p>
                <% end %>
              </div>
            <% else %>
              <p class="text-sm text-gray-600 dark:text-gray-400 transition-colors">
                <%= @product.account.name %>
              </p>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  
  </div>
</div>