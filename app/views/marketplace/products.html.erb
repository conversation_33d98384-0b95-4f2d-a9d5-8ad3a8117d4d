<% content_for :page_title, "Browse Products" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="lg:grid lg:grid-cols-5 lg:gap-8">
  <!-- Filters Sidebar -->
  <div class="lg:col-span-1">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 sticky top-6 transition-colors">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 transition-colors">Filters</h3>
      
      <%= form_with url: marketplace_products_path, method: :get, local: true, id: "filters-form" do |form| %>
        <!-- Search -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-colors">Search</label>
          <%= form.text_field :search, 
              placeholder: "Search products...", 
              value: params[:search],
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors" %>
        </div>

        <!-- Categories -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 transition-colors">Categories</label>
          <div class="space-y-2 max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500">
            <% @categories.each do |category| %>
              <label class="flex items-center">
                <%= check_box_tag "category_ids[]", category.id, 
                    params[:category_ids]&.include?(category.id.to_s), 
                    class: "h-4 w-4",
                    onchange: "document.getElementById('filters-form').submit();" %>
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300 transition-colors"><%= category.name %></span>
              </label>
            <% end %>
          </div>
        </div>

        <!-- Features filtering removed since features are now product-specific -->

        <!-- Pricing Model -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 transition-colors">Pricing Model</label>
          <div class="space-y-2">
            <% @pricing_models.each do |model| %>
              <label class="flex items-center">
                <%= radio_button_tag :pricing_model, model, 
                    params[:pricing_model] == model, 
                    class: "h-4 w-4",
                    onchange: "document.getElementById('filters-form').submit();" %>
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300 transition-colors"><%= model.humanize %></span>
              </label>
            <% end %>
            <label class="flex items-center">
              <%= radio_button_tag :pricing_model, "", 
                  params[:pricing_model].blank?, 
                  class: "h-4 w-4",
                  onchange: "document.getElementById('filters-form').submit();" %>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300 transition-colors">All</span>
            </label>
          </div>
        </div>

        <!-- Company Size -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 transition-colors">Company Size</label>
          <div class="space-y-2">
            <% @company_sizes.each do |size| %>
              <label class="flex items-center">
                <%= radio_button_tag :company_size, size, 
                    params[:company_size] == size, 
                    class: "h-4 w-4",
                    onchange: "document.getElementById('filters-form').submit();" %>
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300 transition-colors"><%= size %> employees</span>
              </label>
            <% end %>
            <label class="flex items-center">
              <%= radio_button_tag :company_size, "", 
                  params[:company_size].blank?, 
                  class: "h-4 w-4",
                  onchange: "document.getElementById('filters-form').submit();" %>
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300 transition-colors">All sizes</span>
            </label>
          </div>
        </div>

        <!-- Sort -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-colors">Sort by</label>
          <div class="mt-2 grid grid-cols-1">
            <%= form.select :sort, 
                options_for_select(@sort_options, params[:sort]),
                { prompt: "Default" },
                { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-800 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm transition-colors",
                  onchange: "document.getElementById('filters-form').submit();" } %>
            <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
              <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>

        <!-- Clear Filters -->
        <div class="pt-4 border-t border-gray-200 dark:border-gray-700 transition-colors">
          <%= link_to "Clear all filters", marketplace_products_path, 
              class: "block w-full text-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Products Grid -->
  <div class="lg:col-span-4">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 transition-colors">Products</h1>
        <p class="text-gray-600 dark:text-gray-400 transition-colors">
          <%= pluralize(@products.count, 'product') %> found
          <% if params[:search].present? %>
            for "<%= params[:search] %>"
          <% end %>
        </p>
      </div>
      
      <!-- Mobile filters toggle -->
      <div class="lg:hidden">
        <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          Filters
        </button>
      </div>
    </div>

    <!-- Applied Filters -->
    <% if params[:search].present? || params[:category_ids].present? || params[:pricing_model].present? || params[:company_size].present? %>
      <div class="mb-6">
        <div class="flex flex-wrap gap-2">
          <% if params[:search].present? %>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition-colors">
              Search: "<%= params[:search] %>"
              <%= link_to marketplace_products_path(request.query_parameters.except(:search)), class: "ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors" do %>
                ×
              <% end %>
            </span>
          <% end %>
          
          <% if params[:category_ids].present? %>
            <% params[:category_ids].reject(&:blank?).each do |category_id| %>
              <% category = @categories.find { |c| c.id == category_id.to_i } %>
              <% if category %>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition-colors">
                  <%= category.name %>
                  <%= link_to marketplace_products_path(request.query_parameters.except(:category_ids)), class: "ml-1 text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 transition-colors" do %>
                    ×
                  <% end %>
                </span>
              <% end %>
            <% end %>
          <% end %>
          
          <% if params[:pricing_model].present? %>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 transition-colors">
              <%= params[:pricing_model].humanize %>
              <%= link_to marketplace_products_path(request.query_parameters.except(:pricing_model)), class: "ml-1 text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 transition-colors" do %>
                ×
              <% end %>
            </span>
          <% end %>
        </div>
      </div>
    <% end %>

    <!-- Products -->
    <% if @products.any? %>
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        <% @products.each do |product| %>
          <div class="group">
            <%= link_to marketplace_product_path(product), 
                class: "block bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden group-hover:border-blue-300 dark:group-hover:border-blue-600" do %>
              <!-- Product Image -->
              <div class="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-700 transition-colors">
                <div class="flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 transition-colors">
                  <span class="text-blue-600 dark:text-blue-400 text-2xl font-bold transition-colors">
                    <%= product.name.first.upcase %>
                  </span>
                </div>
              </div>
              
              <!-- Product Info -->
              <div class="p-6">
                <h3 class="font-semibold text-lg text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-2">
                  <%= truncate(product.name, length: 60) %>
                </h3>
                
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 transition-colors">
                  by <%= product.account.company_profile&.company_name || product.account.name %>
                </p>
                
                <% if product.description.present? %>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 transition-colors">
                    <%= truncate(strip_tags(product.description), length: 120) %>
                  </p>
                <% end %>
                
                <!-- Category and Features -->
                <div class="flex flex-wrap gap-2 mb-4">
                  <% if product.category %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition-colors">
                      <%= product.category.name %>
                    </span>
                  <% end %>
                  <% product.feature_names.first(2).each do |feature_name| %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors">
                      <%= feature_name %>
                    </span>
                  <% end %>
                  <% if product.feature_names.count > 2 %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 transition-colors">
                      +<%= product.feature_names.count - 2 %> more
                    </span>
                  <% end %>
                </div>
                
                <!-- Footer -->
                <div class="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700 transition-colors">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition-colors">
                    <%= product.pricing_model.humanize %>
                  </span>
                  
                  <% if product.case_studies.any? %>
                    <span class="text-xs text-gray-500 dark:text-gray-400 transition-colors">
                      <%= pluralize(product.case_studies.count, 'case study') %>
                    </span>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- No Results -->
      <div class="text-center py-12">
        <div class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4 transition-colors">
          <svg class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 transition-colors">No products found</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6 transition-colors">
          <% if request.query_parameters.values.any?(&:present?) %>
            Try adjusting your search criteria or filters.
          <% else %>
            There are no products available at the moment.
          <% end %>
        </p>
        <% if request.query_parameters.values.any?(&:present?) %>
          <%= link_to "Clear all filters", marketplace_products_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
        <% end %>
      </div>
    <% end %>
  </div>
  </div>
</div>