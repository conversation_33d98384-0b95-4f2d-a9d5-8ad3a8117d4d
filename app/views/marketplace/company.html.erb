<% content_for :page_title, @company.company_name %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Breadcrumb -->
  <nav class="flex mb-6" aria-label="Breadcrumb">
  <ol class="flex items-center space-x-2">
    <li>
      <%= link_to marketplace_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
        Marketplace
      <% end %>
    </li>
    <li>
      <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600 transition-colors" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
      </svg>
    </li>
    <li>
      <%= link_to marketplace_companies_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
        Companies
      <% end %>
    </li>
    <li>
      <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600 transition-colors" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
      </svg>
    </li>
    <li class="text-gray-500 dark:text-gray-400 transition-colors">
      <%= truncate(@company.company_name, length: 50) %>
    </li>
  </ol>
</nav>

<!-- Company Header -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
  <div class="px-6 py-8">
    <div class="flex items-start justify-between">
      <div class="flex items-center">
        <div class="w-20 h-20 bg-blue-100 dark:bg-blue-900 rounded-xl mr-6 flex items-center justify-center transition-colors overflow-hidden">
          <% if @company.logo.attached? %>
            <%= image_tag @company.logo_medium, 
                class: "max-w-full max-h-full object-contain", 
                alt: "#{@company.company_name} logo" %>
          <% else %>
            <span class="text-blue-600 dark:text-blue-400 font-bold text-2xl transition-colors">
              <%= @company.company_name.first.upcase %>
            </span>
          <% end %>
        </div>
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-colors">
            <%= @company.company_name %>
          </h1>
          <% if @company.company_size.present? %>
            <p class="text-lg text-gray-600 dark:text-gray-400 mb-2 transition-colors">
              <%= @company.company_size %> employees
            </p>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Company Description -->
<% if @company.description.present? %>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
    <div class="px-6 py-8">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">About <%= @company.company_name %></h2>
      <div class="prose max-w-none">
        <%= simple_format(@company.description) %>
      </div>
    </div>
  </div>
<% end %>

<!-- Products -->
<% if @products.any? %>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
    <div class="px-6 py-8">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Products & Solutions</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% @products.each do |product| %>
          <div class="group">
            <%= link_to marketplace_product_path(product), 
                class: "block bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors border border-gray-200 dark:border-gray-600 overflow-hidden group-hover:border-blue-300 dark:group-hover:border-blue-600" do %>
              <div class="p-6">
                <h3 class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-2">
                  <%= product.name %>
                </h3>
                <% if product.description.present? %>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 transition-colors">
                    <%= truncate(strip_tags(product.description), length: 100) %>
                  </p>
                <% end %>
                <div class="flex items-center justify-between">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition-colors">
                    <%= product.pricing_model.humanize %>
                  </span>
                  <% if product.category.present? %>
                    <span class="text-xs text-gray-500 dark:text-gray-400 transition-colors">
                      <%= product.category.name %>
                    </span>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
<% else %>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
    <div class="px-6 py-8">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Products & Solutions</h2>
      <div class="text-center py-8">
        <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-xl mx-auto mb-4 flex items-center justify-center transition-colors">
          <svg class="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 transition-colors">No products available</h3>
        <p class="text-gray-500 dark:text-gray-400 transition-colors">
          <%= @company.company_name %> hasn't published any products yet.
        </p>
      </div>
    </div>
  </div>
<% end %>

<!-- Company Details -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors">
  <div class="px-6 py-8">
    <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Company Details</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">


      <% if @company.linkedin_url.present? %>
        <div>
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 transition-colors">LinkedIn</h3>
          <p class="text-lg text-gray-900 dark:text-gray-100 transition-colors">
            <%= link_to @company.linkedin_url, target: '_blank', class: "inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors" do %>
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
              LinkedIn
            <% end %>
          </p>
        </div>
      <% end %>
      
      <% if @company.website.present? %>
        <div>
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 transition-colors">Website</h3>
          <p class="text-lg text-gray-900 dark:text-gray-100 transition-colors">
            <%= link_to @company.website, @company.website, target: '_blank', class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors" %>
          </p>
        </div>
      <% end %>
      
      <% if @company.contact_phone.present? %>
        <div>
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 transition-colors">Phone</h3>
          <p class="text-lg text-gray-900 dark:text-gray-100 transition-colors">
            <%= link_to @company.contact_phone, "tel:#{@company.contact_phone}", class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors" %>
          </p>
        </div>
      <% end %>
      
      <% if @company.contact_email.present? %>
        <div>
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 transition-colors">Email</h3>
          <p class="text-lg text-gray-900 dark:text-gray-100 transition-colors">
            <%= link_to @company.contact_email, "mailto:#{@company.contact_email}", class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors" %>
          </p>
        </div>
      <% end %>
      

           <% if @company.company_size.present? %>
        <div>
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 transition-colors">Company Size</h3>
          <p class="text-lg text-gray-900 dark:text-gray-100 transition-colors"><%= @company.company_size %> employees</p>
        </div>
      <% end %>
      
      <% if @company.headquarters_location.present? %>
        <div>
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 transition-colors">Headquarters</h3>
          <p class="text-lg text-gray-900 dark:text-gray-100 transition-colors"><%= @company.headquarters_location %></p>
        </div>
      <% end %>
    
 

    </div>
  </div>
</div>
</div>