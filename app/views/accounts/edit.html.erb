<% content_for :page_title, "Edit Profile" %>

<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Profile Information Form -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">Profile Information</h3>
      
      <%= form_with model: @user, url: update_profile_account_path, method: :patch, local: true, class: "space-y-6" do |form| %>
        <% if @user.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were <%= pluralize(@user.errors.count, "error") %> with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul role="list" class="list-disc space-y-1 pl-5">
                    <% @user.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <%= form.label :first_name, "First Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :first_name, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
          </div>

          <div>
            <%= form.label :last_name, "Last Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :last_name, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
          </div>

          <div class="sm:col-span-2">
            <%= form.label :email_address, "Email Address", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.email_field :email_address, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
          </div>

          <div>
            <%= form.label :phone, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Phone <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.telephone_field :phone, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
          </div>

          <div>
            <%= form.label :job_title, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Job Title <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.text_field :job_title, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <%= link_to "Cancel", account_path, class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
          <%= form.submit "Update Profile", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Profile Photo Form -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">Profile Photo</h3>
      
      <%= form_with model: @user, url: update_profile_photo_account_path, method: :patch, local: true, multipart: true, class: "space-y-6" do |form| %>
        <% if @user.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were <%= pluralize(@user.errors.count, "error") %> with your photo upload:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul role="list" class="list-disc space-y-1 pl-5">
                    <% @user.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="flex items-start space-x-6">
          <!-- Current Photo -->
          <div class="flex-shrink-0">
            <div class="w-24 h-24 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700">
              <% if @user.profile_photo.attached? %>
                <%= image_tag @user.profile_photo_avatar, 
                    class: "w-full h-full object-cover", 
                    alt: "#{@user.full_name}'s profile photo" %>
              <% else %>
                <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                  <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
              <% end %>
            </div>
          </div>

          <!-- Upload Controls -->
          <div class="flex-1 min-w-0">
            <div class="mb-4">
              <%= form.label :profile_photo, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" do %>
                Choose new photo <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
              <% end %>
              
              <div class="flex items-center space-x-4">
                <div class="flex-1">
                  <%= form.file_field :profile_photo, 
                      accept: "image/jpeg,image/jpg,image/png,image/gif,image/webp",
                      class: "block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/50 dark:file:text-blue-300 dark:hover:file:bg-blue-900/70",
                      id: "profile_photo_input" %>
                </div>
                
                <!-- Remove Photo Button -->
                <% if @user.profile_photo.attached? %>
                  <%= link_to "Remove", remove_profile_photo_account_path, 
                      method: :delete, 
                      data: { confirm: "Are you sure you want to remove your profile photo?" },
                      class: "px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300" %>
                <% end %>
              </div>
            </div>

            <!-- File Requirements -->
            <div class="text-sm text-gray-500 dark:text-gray-400 mb-4">
              <p>• Maximum file size: 5MB</p>
              <p>• Accepted formats: JPEG, PNG, GIF, WebP</p>
              <p>• Recommended: Square image, at least 200x200 pixels</p>
            </div>

            <!-- Preview Area -->
            <div id="photo-preview" class="hidden">
              <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 mb-4">
                <div class="flex items-center space-x-4">
                  <div class="w-16 h-16 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700">
                    <img id="preview-image" class="w-full h-full object-cover" alt="Preview" />
                  </div>
                  <div class="flex-1">
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                      <span class="font-medium">New photo preview</span>
                      <% if @user.profile_photo.attached? %>
                        <br><span class="text-xs text-gray-500 dark:text-gray-400">This will replace your current photo</span>
                      <% end %>
                    </p>
                    <p id="file-info" class="text-xs text-gray-500 dark:text-gray-400 mt-1"></p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3">
              <%= link_to "Cancel", account_path, class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
              <%= form.submit "Update Photo", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800", id: "upload-photo-btn", disabled: true %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Change Password Form -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">Change Password</h3>
      
      <%= form_with model: @user, url: update_password_account_path, method: :patch, local: true, class: "space-y-6" do |form| %>
        <% if @user.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were <%= pluralize(@user.errors.count, "error") %> with your password update:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul role="list" class="list-disc space-y-1 pl-5">
                    <% @user.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <%= form.label :password, "New Password", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.password_field :password, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
          </div>

          <div>
            <%= form.label :password_confirmation, "Confirm New Password", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.password_field :password_confirmation, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <%= form.submit "Update Password", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const fileInput = document.getElementById('profile_photo_input');
  const previewDiv = document.getElementById('photo-preview');
  const previewImage = document.getElementById('preview-image');
  const fileInfo = document.getElementById('file-info');
  const uploadBtn = document.getElementById('upload-photo-btn');
  
  if (fileInput) {
    fileInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      
      if (file) {
        // Validate file size
        if (file.size > 5 * 1024 * 1024) {
          alert('File size must be less than 5MB');
          fileInput.value = '';
          previewDiv.classList.add('hidden');
          uploadBtn.disabled = true;
          return;
        }
        
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          alert('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
          fileInput.value = '';
          previewDiv.classList.add('hidden');
          uploadBtn.disabled = true;
          return;
        }
        
        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
          previewImage.src = e.target.result;
          fileInfo.textContent = `${file.name} (${formatFileSize(file.size)})`;
          previewDiv.classList.remove('hidden');
          uploadBtn.disabled = false;
        };
        reader.readAsDataURL(file);
      } else {
        previewDiv.classList.add('hidden');
        uploadBtn.disabled = true;
      }
    });
  }
  
  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
});
</script>