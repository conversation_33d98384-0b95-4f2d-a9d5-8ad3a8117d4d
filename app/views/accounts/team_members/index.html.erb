<% content_for :page_title, "Team Members" %>

<div class="sm:flex sm:items-center">
  <div class="sm:flex-auto">
    <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Team Members</h1>
    <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Manage your team members and their roles within your organization.</p>
  </div>
  <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
    <% if @account.approved? || current_user.admin? %>
      <%= link_to "Invite Team Member", new_account_team_invitation_path, 
          class: "inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 dark:bg-blue-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 sm:w-auto" %>
    <% else %>
      <div class="text-sm text-gray-500 dark:text-gray-400">
        Account must be approved to invite team members
      </div>
    <% end %>
  </div>
</div>

<!-- Current Team Members -->
<div class="mt-8 flow-root">
  <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
      <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
        <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Member</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Role</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Joined</th>
              <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-600 bg-white dark:bg-gray-800">
            <% @team_members.each do |team_member| %>
              <tr>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                  <div class="flex items-center">
                    <div class="h-10 w-10 flex-shrink-0">
                      <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center">
                        <span class="text-sm font-medium text-blue-800 dark:text-blue-100">
                          <%= team_member.user.first_name&.first %><%= team_member.user.last_name&.first %>
                        </span>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="font-medium text-gray-900 dark:text-gray-100">
                        <%= team_member.user.full_name %>
                        <% if team_member.user == @account.owner %>
                          <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                            Owner
                          </span>
                        <% end %>
                      </div>
                      <div class="text-gray-500 dark:text-gray-400"><%= team_member.user.email_address %></div>
                    </div>
                  </div>
                </td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <%= team_member.role == 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100' %>">
                    <%= team_member.role&.capitalize || 'Member' %>
                  </span>
                </td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                  <%= team_member.joined_at ? time_ago_in_words(team_member.joined_at) + " ago" : "N/A" %>
                </td>
                <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                  <% if team_member.user != @account.owner %>
                    <% if @account.approved? || current_user.admin? %>
                      <div class="flex justify-end space-x-2">
                        <%= link_to "Edit Role", edit_account_team_member_path(team_member), 
                            class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                        <button type="button" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 remove-user-btn" 
                                data-user-id="<%= team_member.id %>" 
                                data-user-name="<%= team_member.user.full_name %>">
                          Remove
                        </button>
                      </div>
                    <% else %>
                      <span class="text-gray-400 dark:text-gray-500">Account pending approval</span>
                    <% end %>
                  <% else %>
                    <span class="text-gray-400 dark:text-gray-500">Owner</span>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Pending Invitations -->
<% if @pending_invitations.any? %>
  <div class="mt-8">
    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Pending Invitations</h3>
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
      <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-600">
        <% @pending_invitations.each do |invitation| %>
          <li class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="h-10 w-10 flex-shrink-0">
                  <div class="h-10 w-10 rounded-full bg-yellow-100 dark:bg-yellow-800 flex items-center justify-center">
                    <svg class="h-5 w-5 text-yellow-600 dark:text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="font-medium text-gray-900 dark:text-gray-100"><%= invitation.email %></div>
                  <div class="text-gray-500 dark:text-gray-400">
                    Invited as <%= invitation.role&.capitalize || 'Member' %> • 
                    Expires <%= time_ago_in_words(invitation.expires_at) %> from now
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                  Pending
                </span>
                <button type="button" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 text-sm cancel-invitation-btn" 
                        data-invitation-id="<%= invitation.id %>" 
                        data-invitation-email="<%= invitation.email %>">
                  Cancel
                </button>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    </div>
  </div>
<% end %>

<!-- Remove User Confirmation Modal -->
<div id="remove-user-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500/75 transition-opacity" aria-hidden="true"></div>
    
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
    
    <div class="relative inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
      <div class="sm:flex sm:items-start">
        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/50 sm:mx-0 sm:h-10 sm:w-10">
          <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
            Remove Team Member
          </h3>
          <div class="mt-2">
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Are you sure you want to remove <span class="font-medium text-gray-900 dark:text-gray-100" id="remove-user-name"></span> from the team? This action cannot be undone and they will lose access to this account.
            </p>
          </div>
        </div>
      </div>
      
      <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
        <button type="button" id="confirm-remove-user-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
          Remove
        </button>
        <button type="button" id="cancel-remove-user-btn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Cancel Invitation Confirmation Modal -->
<div id="cancel-invitation-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500/75 transition-opacity" aria-hidden="true"></div>
    
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
    
    <div class="relative inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
      <div class="sm:flex sm:items-start">
        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/50 sm:mx-0 sm:h-10 sm:w-10">
          <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
            Cancel Invitation
          </h3>
          <div class="mt-2">
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Are you sure you want to cancel the invitation to <span class="font-medium text-gray-900 dark:text-gray-100" id="cancel-invitation-email"></span>? This action cannot be undone and they will not be able to join the team using this invitation.
            </p>
          </div>
        </div>
      </div>
      
      <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
        <button type="button" id="confirm-cancel-invitation-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
          Cancel Invitation
        </button>
        <button type="button" id="cancel-cancel-invitation-btn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm">
          Keep Invitation
        </button>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Remove User Modal
  const removeUserBtns = document.querySelectorAll('.remove-user-btn');
  const removeUserModal = document.getElementById('remove-user-modal');
  const confirmRemoveUserBtn = document.getElementById('confirm-remove-user-btn');
  const cancelRemoveUserBtn = document.getElementById('cancel-remove-user-btn');
  const removeUserName = document.getElementById('remove-user-name');
  
  // Cancel Invitation Modal
  const cancelInvitationBtns = document.querySelectorAll('.cancel-invitation-btn');
  const cancelInvitationModal = document.getElementById('cancel-invitation-modal');
  const confirmCancelInvitationBtn = document.getElementById('confirm-cancel-invitation-btn');
  const cancelCancelInvitationBtn = document.getElementById('cancel-cancel-invitation-btn');
  const cancelInvitationEmail = document.getElementById('cancel-invitation-email');
  
  let currentUserId = null;
  let currentInvitationId = null;
  
  // Show remove user modal
  removeUserBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      currentUserId = this.dataset.userId;
      const userName = this.dataset.userName;
      removeUserName.textContent = userName;
      removeUserModal.classList.remove('hidden');
      document.body.classList.add('overflow-hidden');
    });
  });
  
  // Show cancel invitation modal
  cancelInvitationBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      currentInvitationId = this.dataset.invitationId;
      const invitationEmail = this.dataset.invitationEmail;
      cancelInvitationEmail.textContent = invitationEmail;
      cancelInvitationModal.classList.remove('hidden');
      document.body.classList.add('overflow-hidden');
    });
  });
  
  // Hide modals
  function hideRemoveUserModal() {
    removeUserModal.classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    currentUserId = null;
  }
  
  function hideCancelInvitationModal() {
    cancelInvitationModal.classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    currentInvitationId = null;
  }
  
  // Cancel buttons
  cancelRemoveUserBtn.addEventListener('click', hideRemoveUserModal);
  cancelCancelInvitationBtn.addEventListener('click', hideCancelInvitationModal);
  
  // Close modals when clicking outside
  removeUserModal.addEventListener('click', function(e) {
    if (e.target === removeUserModal || e.target.classList.contains('bg-gray-500')) {
      hideRemoveUserModal();
    }
  });
  
  cancelInvitationModal.addEventListener('click', function(e) {
    if (e.target === cancelInvitationModal || e.target.classList.contains('bg-gray-500')) {
      hideCancelInvitationModal();
    }
  });
  
  // Confirm remove user
  confirmRemoveUserBtn.addEventListener('click', function() {
    if (currentUserId) {
      confirmRemoveUserBtn.disabled = true;
      confirmRemoveUserBtn.textContent = 'Removing...';
      
      // Create and submit form
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = `/account/team_members/${currentUserId}`;
      
      const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
      const csrfInput = document.createElement('input');
      csrfInput.type = 'hidden';
      csrfInput.name = 'authenticity_token';
      csrfInput.value = csrfToken;
      
      const methodInput = document.createElement('input');
      methodInput.type = 'hidden';
      methodInput.name = '_method';
      methodInput.value = 'DELETE';
      
      form.appendChild(csrfInput);
      form.appendChild(methodInput);
      document.body.appendChild(form);
      form.submit();
    }
  });
  
  // Confirm cancel invitation
  confirmCancelInvitationBtn.addEventListener('click', function() {
    if (currentInvitationId) {
      confirmCancelInvitationBtn.disabled = true;
      confirmCancelInvitationBtn.textContent = 'Cancelling...';
      
      // Create and submit form
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = `/account/team_invitations/${currentInvitationId}`;
      
      const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
      const csrfInput = document.createElement('input');
      csrfInput.type = 'hidden';
      csrfInput.name = 'authenticity_token';
      csrfInput.value = csrfToken;
      
      const methodInput = document.createElement('input');
      methodInput.type = 'hidden';
      methodInput.name = '_method';
      methodInput.value = 'DELETE';
      
      form.appendChild(csrfInput);
      form.appendChild(methodInput);
      document.body.appendChild(form);
      form.submit();
    }
  });
  
  // Handle successful actions
  document.addEventListener('turbo:before-visit', function(event) {
    hideRemoveUserModal();
    hideCancelInvitationModal();
  });
  
  // Handle escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      hideRemoveUserModal();
      hideCancelInvitationModal();
    }
  });
});
</script>