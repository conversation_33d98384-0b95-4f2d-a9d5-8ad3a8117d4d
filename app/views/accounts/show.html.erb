<% content_for :page_title, "Account Management" %>

<div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Account Management Section -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-8">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Account Management</h3>
        <% if @user.accounts.any? && current_user_account_admin? %>
          <%= link_to "Manage Team", account_team_members_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
        <% end %>
      </div>
      
      <% if @user.accounts.any? %>
        <% account = @user.accounts.first %>
        <!-- Account Information -->
        <div class="mb-8">
          <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4"><%= account.account_type.capitalize %> Account</h4>
          <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Name</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= account.name %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Type</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <% if account.account_type == 'vendor' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200">
                    Vendor
                  </span>
                <% elsif account.account_type == 'agency' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                    Government Agency
                  </span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <% if account.status == 'approved' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                    Approved
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                    <%= account.status&.titleize || 'Pending' %>
                  </span>
                <% end %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= account.created_at.strftime("%B %-d, %Y") %></dd>
            </div>
          </dl>
        </div>

        <!-- Team Members -->
        <div>
          <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">Team Members</h4>
          <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Role</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Joined</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <% account.account_users.includes(:user).each do |account_user| %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                      <%= account_user.user.full_name %>
                      <% if account_user.user == @user %>
                        <span class="text-xs text-gray-500 dark:text-gray-400">(You)</span>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <%= account_user.user.email_address %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                        <% if account.owner_id == account_user.user.id %>
                          Owner
                        <% else %>
                          <%= account_user.role&.titleize || 'Member' %>
                        <% end %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <%= account_user.joined_at&.strftime("%B %-d, %Y") || account_user.created_at.strftime("%B %-d, %Y") %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      <% else %>
        <div class="text-center py-8">
          <p class="text-sm text-gray-500 dark:text-gray-400">No account found.</p>
        </div>
      <% end %>
    </div>
  </div>
  
  <!-- User Profile Section -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-4">
          <!-- Profile Photo -->
          <div class="w-16 h-16 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700 flex-shrink-0">
            <% if @user.profile_photo.attached? %>
              <%= image_tag @user.profile_photo_avatar, 
                  class: "w-full h-full object-cover", 
                  alt: "#{@user.full_name}'s profile photo" %>
            <% else %>
              <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
            <% end %>
          </div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">User Profile</h3>
        </div>
        <%= link_to "Edit Profile", edit_account_path, 
            class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
      </div>
      
      <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">First Name</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.first_name %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Name</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.last_name %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.email_address %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.phone.present? ? @user.phone : "Not provided" %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Job Title</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.job_title.present? ? @user.job_title : "Not provided" %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Member Since</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.created_at.strftime("%B %-d, %Y") %></dd>
        </div>
      </dl>
    </div>
  </div>
  
  <!-- Sign Out Section -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mt-8">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Sign Out</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Sign out of your account and return to the home page.
          </p>
        </div>
        <div class="ml-6">
          <%= link_to "Sign Out", session_path, 
              data: { "turbo-method": :delete },
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      </div>
    </div>
  </div>
</div>