<% content_for :page_title, "Company Details - #{@company.company_name}" %>

<div class="px-4 sm:px-6 lg:px-8">
  <div class="mx-auto max-w-7xl">
    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between mb-8">
      <div class="min-w-0 flex-1">
        <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl sm:tracking-tight">
          <%= @company.company_name %>
        </h1>
        <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
              <%= @company.published? ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' %>">
              <%= @company.published? ? 'Published' : 'Unpublished' %>
            </span>
          </div>
          <% if @company.company_size.present? %>
            <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                <%= @company.company_size %>
              </span>
            </div>
          <% end %>
        </div>
      </div>
      <div class="mt-4 flex md:ml-4 md:mt-0 space-x-3">
        <%= link_to "Edit Company", edit_admin_company_path(@company), 
            class: "inline-flex items-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600" %>
        
        <%= button_to "Delete Company", admin_company_path(@company), method: :delete,
            class: "inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600",
            data: { 
              turbo_method: :delete,
              turbo_confirm: "Are you sure you want to delete #{@company.company_name}?",
              controller: "modal-confirm"
            } %>
        
        <%= link_to "← Back to Companies", admin_companies_path,
            class: "inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" %>
      </div>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <!-- Company Information -->
      <div class="lg:col-span-2">
        <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Company Information</h3>
            <div class="mt-5 border-t border-gray-200 dark:border-gray-600">
              <dl class="divide-y divide-gray-200 dark:divide-gray-600">
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company Name</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @company.company_name %></dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Website</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                    <%= link_to @company.website, @company.website, target: "_blank", class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                  </dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Contact Email</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @company.contact_email %></dd>
                </div>
                <% if @company.contact_phone.present? %>
                  <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @company.contact_phone %></dd>
                  </div>
                <% end %>
                <% if @company.company_size.present? %>
                  <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company Size</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @company.company_size %></dd>
                  </div>
                <% end %>
                <% if @company.headquarters_location.present? %>
                  <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Headquarters</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @company.headquarters_location %></dd>
                  </div>
                <% end %>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Published</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                    <%= @company.published? ? 'Yes' : 'No' %>
                  </dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @company.created_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
                </div>
              </dl>
            </div>
          </div>
        </div>

        <!-- Description -->
        <% if @company.description.present? %>
          <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Description</h3>
              <div class="mt-3 prose prose-sm max-w-none text-gray-900 dark:text-gray-100">
                <%= @company.description %>
              </div>
            </div>
          </div>
        <% end %>

      </div>

      <!-- Company Logo & Account Info -->
      <div class="lg:col-span-1">
        <!-- Company Logo -->
        <% if @company.logo.attached? %>
          <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Logo</h3>
              <div class="mt-3 flex justify-center">
                <%= image_tag @company.logo_large, class: "h-32 w-auto object-contain" %>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Account Information -->
        <div class="<%= @company.logo.attached? ? 'mt-6' : '' %> bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Account Information</h3>
            <div class="mt-3">
              <div class="text-sm">
                <p class="font-medium text-gray-900 dark:text-gray-100">
                  <%= link_to @company.account.name, admin_account_path(@company.account), class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                </p>
                <p class="text-gray-500 dark:text-gray-400">
                  <%= @company.account.account_type.capitalize %> Account
                </p>
                <p class="text-gray-500 dark:text-gray-400">
                  Status: <%= @company.account.status.capitalize %>
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Quick Stats</h3>
            <dl class="mt-3 grid grid-cols-1 gap-3">
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Products</dt>
                <dd class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @company.account.products.count %></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Leads</dt>
                <dd class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @company.account.leads.count %></dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>