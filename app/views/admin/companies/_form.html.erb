<%= form_with model: [:admin, @company], url: form_url, local: true, class: "space-y-6" do |form| %>
  <% if @company.errors.any? %>
    <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were <%= pluralize(@company.errors.count, "error") %> with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul class="list-disc pl-5 space-y-1">
              <% @company.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Company Name -->
  <div>
    <%= form.label :company_name, "Company Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.text_field :company_name, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "Enter company name" %>
  </div>

  <!-- Account -->
  <div>
    <%= form.label :account_id, "Account", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <div class="mt-2 grid grid-cols-1">
      <%= form.select :account_id,
          options_from_collection_for_select(@accounts, :id, :name, @company.account_id),
          { include_blank: "Select an account" },
          { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm" } %>
      <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
        <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
      </svg>
    </div>
  </div>

  <!-- Website -->
  <div>
    <%= form.label :website, "Website", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.url_field :website, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "https://example.com" %>
  </div>

  <!-- Contact Email -->
  <div>
    <%= form.label :contact_email, "Contact Email", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.email_field :contact_email, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "<EMAIL>" %>
  </div>

  <!-- Phone -->
  <div>
    <%= form.label :contact_phone, "Phone", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.text_field :contact_phone, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "Enter phone number" %>
  </div>

  <!-- Company Size -->
  <div>
    <%= form.label :company_size, "Company Size", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <div class="mt-2 grid grid-cols-1">
      <%= form.select :company_size,
          options_for_select([['Select company size', '']] + CompanyProfile::COMPANY_SIZES.map { |size| [size, size] }, @company.company_size),
          {},
          { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm" } %>
      <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
        <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
      </svg>
    </div>
  </div>

  <!-- Headquarters Location -->
  <div>
    <%= form.label :headquarters_location, "Headquarters Location", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.text_field :headquarters_location, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "City, State/Province, Country" %>
  </div>

  <!-- Description -->
  <div>
    <%= form.label :description, "Description", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.rich_text_area :description, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "Describe the company..." %>
  </div>

  <!-- Logo -->
  <div class="flex items-start space-x-6">
    <div class="flex-shrink-0">
      <div class="w-24 h-16 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 flex items-center justify-center">
        <% if @company.logo.attached? %>
          <%= image_tag @company.logo_small, class: "max-w-full max-h-full object-contain", alt: "Company logo" %>
        <% else %>
          <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        <% end %>
      </div>
    </div>
    <div class="flex-1 min-w-0">
      <%= form.label :logo, "Company Logo", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
      <div class="text-sm text-gray-500 dark:text-gray-400 mb-4">
        <p>• Maximum file size: 5MB</p>
        <p>• Accepted formats: JPEG, PNG, GIF, WebP, SVG</p>
        <p>• Recommended: Square or landscape, minimum 200px width</p>
      </div>
      <%= form.file_field :logo, 
          class: "block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/50 dark:file:text-blue-300 dark:hover:file:bg-blue-900/70",
          accept: "image/jpeg,image/jpg,image/png,image/gif,image/webp,image/svg+xml" %>
    </div>
  </div>


  <!-- Publishing Options Section -->
  <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
    <div class="flex items-center">
      <%= form.check_box :published, class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded" %>
      <%= form.label :published, "Published", class: "ml-2 block text-sm text-gray-900 dark:text-gray-100" %>
      <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">Make this company profile visible to the public</p>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600">
    <%= link_to "Cancel", cancel_url, 
        class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
    
    <%= form.submit submit_text, 
        class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
  </div>
<% end %>