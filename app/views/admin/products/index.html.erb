<% content_for :page_title, "Product Management" %>

<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Management</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Manage products and their listings across all vendors.
      </p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <%= link_to "New Product", new_admin_product_path, 
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-md mb-6 transition-colors">
    <div class="px-6 py-4">
      <%= form_with url: admin_products_path, method: :get, local: true, class: "flex flex-wrap gap-4 items-end" do |form| %>
        <div class="flex-1 min-w-64">
          <%= form.label :search, "Search", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_field :search, 
              placeholder: "Search by product name, description, or company...", 
              value: params[:search],
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
        </div>
        
        <div>
          <%= form.label :published, "Status", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <div class="mt-2 grid grid-cols-1">
            <%= form.select :published,
                options_for_select([
                  ['All Status', ''],
                  ['Published', 'true'],
                  ['Unpublished', 'false']
                ], params[:published]),
                {},
                { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm transition-colors" } %>
            <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
              <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>


        <div>
          <%= form.label :pricing_model, "Pricing Model", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <div class="mt-2 grid grid-cols-1">
            <%= form.select :pricing_model,
                options_for_select([
                  ['All Models', '']
                ] + Product.pricing_models.map { |key, value| [value, key] }, params[:pricing_model]),
                {},
                { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm transition-colors" } %>
            <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
              <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>

        <div>
          <%= form.submit "Filter", 
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
        
        <% if params.values.any?(&:present?) %>
          <div>
            <%= link_to "Clear", admin_products_path, 
                class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Products Table -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Product</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Company</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Pricing</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Created</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @products.each do |product| %>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                    <div class="flex items-center">
                      <div>
                        <div class="font-medium text-gray-900 dark:text-gray-100">
                          <%= link_to product.name, admin_product_path(product), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                        </div>
                        <div class="text-gray-500 dark:text-gray-400 text-xs">
                          <%= truncate(strip_tags(product.description.to_s), length: 50) %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-900 dark:text-gray-100">
                    <div class="flex items-center">
                      <% if product.account.company_profile&.logo&.attached? %>
                        <div class="h-8 w-8 flex-shrink-0 mr-2">
                          <%= image_tag product.account.company_profile.logo_thumbnail, class: "h-8 w-8 rounded-full object-cover" %>
                        </div>
                      <% end %>
                      <div>
                        <div class="font-medium text-gray-900 dark:text-gray-100">
                          <%= link_to product.account.company_profile&.company_name || product.account.name, admin_company_path(product.account.company_profile), class: "hover:text-blue-600 dark:hover:text-blue-400" if product.account.company_profile %>
                          <%= product.account.name unless product.account.company_profile %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <% if product.pricing_model.present? %>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                        <%= Product.pricing_models[product.pricing_model] %>
                      </span>
                    <% end %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                      <%= product.published? ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' %>">
                      <%= product.published? ? 'Published' : 'Unpublished' %>
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <%= product.created_at.strftime("%B %-d, %Y") %>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <div class="flex justify-end space-x-2">
                      <%= link_to "View", admin_product_path(product), class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                      <%= link_to "Edit", edit_admin_product_path(product), class: "text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300" %>
                      <%= button_to "Delete", admin_product_path(product), method: :delete,
                          class: "text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 bg-transparent border-0 p-0 text-sm font-medium underline",
                          data: { 
                            turbo_method: :delete,
                            turbo_confirm: "Are you sure you want to delete #{product.name}?",
                            controller: "modal-confirm"
                          } %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <%= render partial: "shared/pagination", locals: { pagy: @pagy } %>

  <% if @products.empty? %>
    <div class="text-center py-8">
      <p class="text-sm text-gray-500 dark:text-gray-400">No products found.</p>
    </div>
  <% end %>
</div>