<% content_for :page_title, "Edit Product - #{@product.name}" %>

<div class="px-4 sm:px-6 lg:px-8">
  <div class="mx-auto max-w-3xl">
    <!-- Header -->
    <div class="mb-8">
      <div class="md:flex md:items-center md:justify-between">
        <div class="min-w-0 flex-1">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl sm:tracking-tight">
            Edit Product
          </h1>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Update product information for <%= @product.name %>
          </p>
        </div>
        <div class="mt-4 flex md:ml-4 md:mt-0">
          <%= link_to "← Back to Product", admin_product_path(@product),
              class: "inline-flex items-center rounded-md bg-gray-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600" %>
        </div>
      </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <%= render 'form', 
            form_url: admin_product_path(@product), 
            submit_text: 'Update Product',
            cancel_url: admin_product_path(@product) %>
      </div>
    </div>
  </div>
</div>