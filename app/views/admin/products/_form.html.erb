<%= form_with model: [:admin, @product], url: form_url, local: true, class: "space-y-6" do |form| %>
  <% if @product.errors.any? %>
    <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were <%= pluralize(@product.errors.count, "error") %> with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul class="list-disc pl-5 space-y-1">
              <% @product.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Product Name -->
  <div>
    <%= form.label :name, "Product Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.text_field :name, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "Enter product name" %>
  </div>

  <!-- Account -->
  <div>
    <%= form.label :account_id, "Account", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <div class="mt-2 grid grid-cols-1">
      <%= form.select :account_id,
          options_from_collection_for_select(@accounts, :id, :name, @product.account_id),
          { include_blank: "Select an account" },
          { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm" } %>
      <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
        <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
      </svg>
    </div>
  </div>

  <!-- Description -->
  <div>
    <%= form.label :description, "Description", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.rich_text_area :description, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "Describe the product..." %>
  </div>

  <!-- Pricing Model -->
  <div>
    <%= form.label :pricing_model, "Pricing Model", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <div class="mt-2 grid grid-cols-1">
      <%= form.select :pricing_model,
          options_for_select([['Select pricing model', '']] + Product.pricing_models.map { |key, value| [value, key] }, @product.pricing_model),
          {},
          { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm" } %>
      <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
        <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
      </svg>
    </div>
  </div>

  <!-- Categories -->
  <div>
    <%= form.label :category_ids, "Categories", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <div class="mt-2 space-y-2 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-3 bg-gray-50 dark:bg-gray-900">
      <% @categories.each do |category| %>
        <div class="flex items-center">
          <%= check_box_tag 'product[category_ids][]', category.id, @product.category_ids.include?(category.id), 
              class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded" %>
          <%= label_tag "product_category_ids_#{category.id}", category.name, class: "ml-2 text-sm text-gray-900 dark:text-gray-100" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Features -->
  <div>
    <%= form.label :features_input, "Features", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <div class="mt-2">
      <%= text_field_tag "product[features_input]",
          @product.feature_names.join(', '),
          placeholder: "Enter features separated by commas (e.g., Cloud-based, Mobile App, API Integration)",
          class: "block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Add custom features that describe this product. Separate multiple features with commas.
      </p>
    </div>
  </div>

  <!-- Publishing Options Section -->
  <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
    <div class="flex items-center">
      <%= form.check_box :published, class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded" %>
      <%= form.label :published, "Published", class: "ml-2 block text-sm text-gray-900 dark:text-gray-100" %>
      <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">Make this product visible to the public</p>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600">
    <%= link_to "Cancel", cancel_url, 
        class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
    
    <%= form.submit submit_text, 
        class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
  </div>
<% end %>