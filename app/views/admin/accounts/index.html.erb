<% content_for :page_title, "Account Management" %>

<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Account Management</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Manage and approve accounts for vendors and government agencies.
      </p>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-md mb-6 transition-colors">
    <div class="px-6 py-4">
      <%= form_with url: admin_accounts_path, method: :get, local: true, class: "flex flex-wrap gap-4 items-end" do |form| %>
        <div class="flex-1 min-w-64">
          <%= form.label :search, "Search", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_field :search, 
              placeholder: "Search by account name, owner name, or email...", 
              value: params[:search],
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
        </div>
        
        <div>
          <%= form.label :status, "Status", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <div class="mt-2 grid grid-cols-1">
            <%= form.select :status,
                options_for_select([
                  ['All Statuses', ''],
                  ['Pending', 'pending'],
                  ['Approved', 'approved'],
                  ['Rejected', 'rejected']
                ], params[:status]),
                {},
                { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm transition-colors" } %>
            <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
              <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>

        <div>
          <%= form.label :account_type, "Account Type", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <div class="mt-2 grid grid-cols-1">
            <%= form.select :account_type,
                options_for_select([
                  ['All Types', ''],
                  ['Vendor', 'vendor'],
                  ['Agency', 'agency']
                ], params[:account_type]),
                {},
                { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm transition-colors" } %>
            <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
              <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>

        <div>
          <%= form.submit "Filter", 
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
        
        <% if params.values.any?(&:present?) %>
          <div>
            <%= link_to "Clear", admin_accounts_path, 
                class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Accounts Table -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Account</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Owner</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Type</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Created</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @accounts.each do |account| %>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                    <div class="flex items-center">
                      <div>
                        <div class="font-medium text-gray-900 dark:text-gray-100">
                          <%= link_to account.name, admin_account_path(account), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                        </div>
                        <div class="text-gray-500 dark:text-gray-400">ID: <%= account.id %></div>
                      </div>
                    </div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-900 dark:text-gray-100">
                    <div class="font-medium"><%= account.owner.full_name %></div>
                    <div class="text-gray-500 dark:text-gray-400"><%= account.owner.email_address %></div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <%= account.vendor? ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-800 dark:text-indigo-100' : 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100' %>">
                      <%= account.account_type.capitalize %>
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                      <%= case account.status
                          when 'approved' then 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                          when 'rejected' then 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                          else 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                          end %>">
                      <%= account.status.capitalize %>
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <%= account.created_at.strftime("%B %-d, %Y") %>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <div class="flex justify-end space-x-2">
                      <%= link_to "View", admin_account_path(account), class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                      
                      <% if account.pending? %>
                        <%= button_to "Approve", approve_admin_account_path(account), method: :post,
                            class: "text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 bg-transparent border-0 p-0 text-sm font-medium underline",
                            data: { confirm: "Are you sure you want to approve #{account.name}?" } %>
                        <%= button_to "Reject", reject_admin_account_path(account), method: :post,
                            class: "text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 bg-transparent border-0 p-0 text-sm font-medium underline ml-2",
                            data: { confirm: "Are you sure you want to reject #{account.name}?" } %>
                      <% elsif account.rejected? %>
                        <%= button_to "Approve", approve_admin_account_path(account), method: :post,
                            class: "text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 bg-transparent border-0 p-0 text-sm font-medium underline",
                            data: { confirm: "Are you sure you want to approve #{account.name}?" } %>
                      <% elsif account.approved? %>
                        <%= button_to "Reject", reject_admin_account_path(account), method: :post,
                            class: "text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 bg-transparent border-0 p-0 text-sm font-medium underline",
                            data: { confirm: "Are you sure you want to reject #{account.name}?" } %>
                      <% end %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <%= render partial: "shared/pagination", locals: { pagy: @pagy } %>

  <% if @accounts.empty? %>
    <div class="text-center py-8">
      <p class="text-sm text-gray-500 dark:text-gray-400">No accounts found.</p>
    </div>
  <% end %>
</div>