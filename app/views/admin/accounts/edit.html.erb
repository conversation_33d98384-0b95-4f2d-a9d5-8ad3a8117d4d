<% content_for :page_title, "Edit Account - #{@account.name}" %>

<div class="px-4 sm:px-6 lg:px-8">
  <div class="mx-auto max-w-3xl">
    <!-- Header -->
    <div class="mb-8">
      <div class="md:flex md:items-center md:justify-between">
        <div class="min-w-0 flex-1">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl sm:tracking-tight">
            Edit Account
          </h1>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Update account details and status for <%= @account.name %>
          </p>
        </div>
        <div class="mt-4 flex md:ml-4 md:mt-0">
          <%= link_to "← Back to Account", admin_account_path(@account),
              class: "inline-flex items-center rounded-md bg-gray-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600" %>
        </div>
      </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <%= form_with model: [:admin, @account], local: true, class: "space-y-6" do |form| %>
          <% if @account.errors.any? %>
            <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
              <div class="flex">
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                    There were <%= pluralize(@account.errors.count, "error") %> with your submission:
                  </h3>
                  <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                    <ul class="list-disc pl-5 space-y-1">
                      <% @account.errors.full_messages.each do |message| %>
                        <li><%= message %></li>
                      <% end %>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          <% end %>

          <!-- Account Name -->
          <div>
            <%= form.label :name, "Account Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :name, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                placeholder: "Enter account name" %>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">The display name for this account</p>
          </div>

          <!-- Account Type -->
          <div>
            <%= form.label :account_type, "Account Type", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :account_type,
                  options_for_select([
                    ['Vendor', 'vendor'],
                    ['Agency', 'agency']
                  ], @account.account_type),
                  { include_blank: false },
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">The type of account - determines available features and dashboards</p>
          </div>

          <!-- Account Status -->
          <div>
            <%= form.label :status, "Account Status", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :status,
                  options_for_select([
                    ['Pending Approval', 'pending'],
                    ['Approved', 'approved'],
                    ['Rejected', 'rejected']
                  ], @account.status),
                  { include_blank: false },
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Account approval status - approved accounts can invite team members</p>
          </div>

          <!-- Current Status Info -->
          <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Current Status Information</h4>
            <dl class="grid grid-cols-1 gap-3 sm:grid-cols-2">
              <div>
                <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Current Status</dt>
                <dd class="mt-1">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    <%= case @account.status
                        when 'approved' then 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                        when 'rejected' then 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                        else 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                        end %>">
                    <%= @account.status.capitalize %>
                  </span>
                </dd>
              </div>
              <% if @account.approved_at %>
                <div>
                  <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Status Changed</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @account.approved_at.strftime("%B %-d, %Y") %></dd>
                </div>
              <% end %>
              <% if @account.approved_by %>
                <div>
                  <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Changed By</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @account.approved_by.full_name %></dd>
                </div>
              <% end %>
              <div>
                <dt class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Account Owner</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @account.owner.full_name %></dd>
              </div>
            </dl>
          </div>

          <!-- Warning for Status Changes -->
          <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Important Notes About Status Changes
                </h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                  <ul class="list-disc pl-5 space-y-1">
                    <li>Changing status to "Approved" will allow the account owner to invite team members</li>
                    <li>Changing status to "Rejected" will prevent the account from accessing most features</li>
                    <li>Status changes are tracked and will update the "approved_at" and "approved_by" fields</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600">
            <%= link_to "Cancel", admin_account_path(@account), 
                class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
            
            <%= form.submit "Update Account", 
                class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>