<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Reset Your Password - Platia</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: linear-gradient(135deg, #7c3aed, #5b21b6); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
    .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
    .footer { background: #f9fafb; padding: 20px; text-align: center; font-size: 14px; color: #6b7280; border-radius: 0 0 8px 8px; }
    .button { display: inline-block; background: #7c3aed; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; margin: 20px 0; }
    .button:hover { background: #5b21b6; }
    .security-box { background: #fef2f2; border: 1px solid #fca5a5; border-radius: 6px; padding: 16px; margin: 20px 0; color: #991b1b; }
  </style>
</head>
<body>
  <div class="header">
    <h1>🔒 Password Reset Request</h1>
  </div>
  
  <div class="content">
    <h2>Hello <%= @user.first_name %>,</h2>
    
    <p>We received a request to reset the password for your Platia account.</p>
    
    <p>To reset your password, click the button below:</p>
    
    <a href="<%= @reset_url %>" class="button">Reset My Password</a>
    
    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
    <p style="word-break: break-all; color: #7c3aed;"><%= @reset_url %></p>
    
    <div class="security-box">
      <p><strong>Security Notice:</strong></p>
      <ul>
        <li>This password reset link will expire in 2 hours</li>
        <li>The link can only be used once</li>
        <li>If you didn't request this reset, you can safely ignore this email</li>
        <li>Never share this link with anyone</li>
      </ul>
    </div>
    
    <p>If you didn't request a password reset, your account is still secure. Someone may have entered your email address by mistake.</p>
    
    <p>If you continue to receive unwanted password reset emails, please contact our support team.</p>
    
    <p>Best regards,<br>
    The Platia Team</p>
  </div>
  
  <div class="footer">
    <p>This email was sent to <%= @user.email_address %></p>
    <p>Platia | Connecting Government with Innovation</p>
    <p>If you have questions, contact <NAME_EMAIL></p>
  </div>
</body>
</html>