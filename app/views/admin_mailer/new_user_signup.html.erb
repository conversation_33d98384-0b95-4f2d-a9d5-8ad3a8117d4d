<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>New User Registration - Admin Alert</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
    .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
    .footer { background: #f9fafb; padding: 20px; text-align: center; font-size: 14px; color: #6b7280; border-radius: 0 0 8px 8px; }
    .button { display: inline-block; background: #dc2626; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; margin: 20px 0; }
    .button:hover { background: #b91c1c; }
    .action-required { background: #fef2f2; border: 1px solid #fca5a5; border-radius: 6px; padding: 16px; margin: 20px 0; color: #991b1b; }
    .user-details { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 6px; padding: 16px; margin: 20px 0; }
  </style>
</head>
<body>
  <div class="header">
    <h1>🚨 New User Registration</h1>
  </div>
  
  <div class="content">
    <h2>Admin Action Required</h2>
    
    <div class="action-required">
      <strong>⚠️ User Approval Needed:</strong> A new user has registered and requires administrative approval before accessing the platform.
    </div>
    
    <p>A new user has completed registration on Platia and is awaiting account approval.</p>
    
    <div class="user-details">
      <h3>👤 User Information</h3>
      <ul>
        <li><strong>Name:</strong> <%= @user.full_name %></li>
        <li><strong>Email:</strong> <%= @user.email_address %></li>
        <li><strong>Registration Date:</strong> <%= @user.created_at.strftime("%B %d, %Y at %I:%M %p") %></li>
        <li><strong>Email Confirmed:</strong> <%= @user.confirmed? ? 'Yes' : 'No' %></li>
        <li><strong>Status:</strong> Pending Approval</li>
      </ul>
      
      <% if @user.accounts.any? %>
        <h4>🏢 Associated Accounts</h4>
        <% @user.accounts.each do |account| %>
          <ul>
            <li><strong>Account Name:</strong> <%= account.name %></li>
            <li><strong>Account Type:</strong> <%= account.account_type.humanize %></li>
            <li><strong>Account Status:</strong> <%= account.status.humanize %></li>
          </ul>
        <% end %>
      <% end %>
    </div>
    
    <h3>🔍 Required Actions</h3>
    <ul>
      <li>Review the user's information for completeness and accuracy</li>
      <li>Verify the legitimacy of the email domain and organization</li>
      <li>Check for any potential security concerns or duplicate accounts</li>
      <li>Approve or reject the user's access request</li>
    </ul>
    
    <div style="text-align: center; margin: 30px 0;">
      <a href="<%= @admin_user_url %>" class="button">Review User Details</a>
    </div>
    
    <p><strong>Security Reminder:</strong> Only approve users from legitimate government agencies and verified technology vendors. When in doubt, request additional verification.</p>
    
    <p>Access the full admin dashboard to manage all pending approvals and user accounts.</p>
    
    <div style="text-align: center; margin: 20px 0;">
      <a href="<%= @admin_users_url %>" style="color: #dc2626; text-decoration: none;">View All Users →</a>
    </div>
  </div>
  
  <div class="footer">
    <p>This is an automated admin notification from Platia</p>
    <p>Admin Dashboard | User Management System</p>
  </div>
</body>
</html>