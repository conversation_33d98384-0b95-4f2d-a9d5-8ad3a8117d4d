<%= form_with model: [@product], url: form_url, local: true, class: "space-y-6" do |form| %>
  <% if @product.errors.any? %>
    <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-md p-4">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were <%= pluralize(@product.errors.count, "error") %> with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul role="list" class="list-disc space-y-1 pl-5">
              <% @product.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="grid grid-cols-1 gap-6">
    <div>
      <%= form.label :name, "Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
      <%= form.text_field :name, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
    </div>

    <div>
      <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
        Description <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
      <% end %>
      <%= form.rich_text_area :description, class: "mt-1 block w-full" %>
    </div>

    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
      <div>
        <%= form.label :pricing_model, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
          Pricing Model <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
        <% end %>
        <div class="mt-2 grid grid-cols-1">
          <%= form.select :pricing_model, 
              options_for_select([
                ['One-time purchase', 'one-time'],
                ['Monthly subscription', 'monthly'],
                ['Annual subscription', 'annual'],
                ['Usage-based', 'usage-based'],
                ['Freemium', 'freemium'],
                ['Enterprise license', 'enterprise'],
                ['Contact for pricing', 'contact']
              ], @product.pricing_model),
              { include_blank: 'Select pricing model' },
              { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm transition-colors" } %>
          <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
            <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>

      <div>
        <%= form.label :category_id, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
          Category <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
        <% end %>
        <div class="mt-2 grid grid-cols-1">
          <%= form.select :category_id, 
              options_from_collection_for_select(@categories, :id, :name, @product.category_id),
              { include_blank: 'Select a category' },
              { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm transition-colors" } %>
          <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
            <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
    </div>

    <div>
      <%= form.label :features_input, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
        Features <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
      <% end %>
      <div class="mt-2">
        <%= text_field_tag "product[features_input]",
            @product.feature_names.join(', '),
            placeholder: "Enter features separated by commas (e.g., Cloud-based, Mobile App, API Integration)",
            class: "block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Add custom features that describe your product. Separate multiple features with commas.
        </p>
      </div>
    </div>
  </div>

  <!-- Publishing Options Section -->
  <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
    <div class="flex items-center">
      <%= form.check_box :published, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" %>
      <%= form.label :published, class: "ml-2 block text-sm text-gray-900 dark:text-gray-100" do %>
        Publish product (make it visible to government users) <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(You can change this later)</span>
      <% end %>
    </div>
  </div>

  <div class="flex justify-end space-x-3">
    <%= link_to "Cancel", vendors_products_path, class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
    <%= form.submit submit_text, class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
  </div>
<% end %>