<div class="max-w-7xl mx-auto">
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="min-w-0 flex-1">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl sm:tracking-tight">
        <%= @product.name %>
      </h2>
      <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
        <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <%= @product.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
            <%= @product.published? ? 'Published' : 'Draft' %>
          </span>
        </div>
      </div>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0">
      <div class="flex space-x-2">
        <%= link_to 'Edit Product', edit_vendors_product_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        <%= link_to 'Back to Products', vendors_products_path, class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <% if @product.description.present? %>
        <div class="mb-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-2">Description</h3>
          <div class="text-gray-700 dark:text-gray-300">
            <%= @product.description %>
          </div>
        </div>
      <% end %>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-2">Product Details</h3>
          <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Pricing Model</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 capitalize"><%= @product.pricing_model %></dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Product Videos Section -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mt-8 transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Product Videos</h3>
        <div class="flex space-x-2">
          <%= link_to 'Manage Videos', vendors_product_product_videos_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          <%= link_to 'Add Video', new_vendors_product_product_video_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>

      <% if @product.product_videos.any? %>
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
            <thead class="bg-gray-50 dark:bg-gray-900/50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Title</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Created</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @product.product_videos.by_position.limit(6).each do |video| %>
                <tr>
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                    <%= video.title %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= video.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                      <%= video.published? ? 'Published' : 'Draft' %>
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <%= video.created_at.strftime("%b %d, %Y") %>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <%= link_to 'View details', vendors_product_product_video_path(@product, video), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <% if @product.product_videos.count > 6 %>
          <div class="mt-6 text-center">
            <%= link_to "View all #{@product.product_videos.count} videos", vendors_product_product_videos_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
          </div>
        <% end %>
      <% else %>
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No videos</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first product video.</p>
          <div class="mt-6">
            <%= link_to 'Add Video', new_vendors_product_product_video_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Case Studies Section -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mt-8 transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Case Studies</h3>
        <div class="flex space-x-2">
          <%= link_to 'Manage Case Studies', vendors_product_case_studies_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          <%= link_to 'Add Case Study', new_vendors_product_case_study_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>

      <% if @product.case_studies.any? %>
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
            <thead class="bg-gray-50 dark:bg-gray-900/50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Title</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Created</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @product.case_studies.by_position.limit(6).each do |case_study| %>
                <tr>
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                    <%= case_study.title %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= case_study.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                      <%= case_study.published? ? 'Published' : 'Draft' %>
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <%= case_study.created_at.strftime("%b %d, %Y") %>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <%= link_to 'View details', vendors_product_case_study_path(@product, case_study), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <% if @product.case_studies.count > 6 %>
          <div class="mt-6 text-center">
            <%= link_to "View all #{@product.case_studies.count} case studies", vendors_product_case_studies_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
          </div>
        <% end %>
      <% else %>
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No case studies</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first case study.</p>
          <div class="mt-6">
            <%= link_to 'Add Case Study', new_vendors_product_case_study_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Product Content Section -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mt-8 transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Product Content</h3>
        <div class="flex space-x-2">
          <%= link_to 'Manage Content', vendors_product_product_contents_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          <%= link_to 'Add Content', new_vendors_product_product_content_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>

      <% if @product.product_content.any? %>
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
            <thead class="bg-gray-50 dark:bg-gray-900/50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Title</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Created</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @product.product_content.by_position.limit(6).each do |content| %>
                <tr>
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                    <%= content.title %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= content.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                      <%= content.published? ? 'Published' : 'Draft' %>
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <%= content.created_at.strftime("%b %d, %Y") %>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <%= link_to 'View details', vendors_product_product_content_path(@product, content), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <% if @product.product_content.count > 6 %>
          <div class="mt-6 text-center">
            <%= link_to "View all #{@product.product_content.count} content items", vendors_product_product_contents_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
          </div>
        <% end %>
      <% else %>
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No content</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first product content.</p>
          <div class="mt-6">
            <%= link_to 'Add Content', new_vendors_product_product_content_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

</div>