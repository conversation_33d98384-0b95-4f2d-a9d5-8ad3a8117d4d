<% content_for :page_title, "Dashboard Overview" %>

<!-- Stats overview -->
<div class="mb-8">
  <dl class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
    <div class="overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:p-6 transition-colors">
      <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Total Products</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900 dark:text-gray-100"><%= @stats[:products_count] %></dd>
    </div>
    <div class="overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:p-6 transition-colors">
      <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Published Products</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900 dark:text-gray-100"><%= @stats[:published_products] %></dd>
    </div>
    <div class="overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:p-6 transition-colors">
      <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Total Leads</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900 dark:text-gray-100"><%= @stats[:total_leads] %></dd>
    </div>
    <div class="overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:p-6 transition-colors">
      <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">New Leads</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900 dark:text-gray-100"><%= @stats[:new_leads] %></dd>
    </div>
  </dl>
</div>

<!-- Recent Leads -->
<div class="mt-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Recent Leads</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Latest inquiries about your products</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
      <%= link_to "View All Leads", vendors_leads_path, 
          class: "inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors" %>
    </div>
  </div>

  <div class="mt-6">
    <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
      <% if @recent_leads.any? %>
        <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Contact</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Product</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Company</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Submitted</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <% @recent_leads.each do |lead| %>
              <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 text-sm">
                  <div class="flex items-center">
                    <div class="h-10 w-10 flex-shrink-0">
                      <div class="h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                          <%= lead.contact_name.split(' ').map(&:first).join('').upcase %>
                        </span>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="font-medium text-gray-900 dark:text-gray-100">
                        <%= lead.contact_name %>
                      </div>
                      <div class="text-gray-500 dark:text-gray-400">
                        <%= lead.contact_email %>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 text-sm">
                  <div class="font-medium text-gray-900 dark:text-gray-100">
                    <%= lead.product.name %>
                  </div>
                  <% if lead.product.category.present? %>
                    <div class="flex flex-wrap gap-1 mt-1">
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                        <%= lead.product.category.name %>
                      </span>
                    </div>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= lead.contact_company %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    <%= case lead.status
                        when 'pending' then 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                        when 'contacted' then 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                        when 'qualified' then 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                        when 'closed' then 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                        else 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                        end %>">
                    <%= lead.status.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= time_ago_in_words(lead.created_at) %> ago
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <%= link_to "View Details", vendors_lead_path(lead), 
                      class: "text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5m-2 3v5m-6 1c2.808 0 5.58-.86 7.94-2.46l2.66 2.66c1.13-1.13 1.13-2.98 0-4.1l-2.66-2.66C17.86 14.58 15.08 13.6 12.3 13.6 8.62 13.6 5.6 10.58 5.6 6.9S8.62.2 12.3.2s6.7 3.02 6.7 6.7-3.02 6.7-6.7 6.7z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No leads yet</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Leads will appear here when customers express interest in your products.</p>
        </div>
      <% end %>
    </div>
  </div>
</div>