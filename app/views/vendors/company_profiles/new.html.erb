<% content_for :page_title, "Create Company Profile" %>

<div class="max-w-3xl mx-auto">
  <div class="bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 mb-6">Create Company Profile</h3>
      
      <%= form_with model: [@company_profile], url: vendors_company_profile_path, local: true, class: "space-y-6" do |form| %>
        <% if @company_profile.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  There were <%= pluralize(@company_profile.errors.count, "error") %> with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul role="list" class="list-disc space-y-1 pl-5">
                    <% @company_profile.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div class="sm:col-span-2">
            <%= form.label :company_name, "Company Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :company_name, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div class="sm:col-span-2">
            <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Description <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.rich_text_area :description, class: "mt-1 block w-full" %>
          </div>

          <div>
            <%= form.label :website, "Website URL", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.url_field :website, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors", placeholder: "https://www.example.com" %>
          </div>

          <div>
            <%= form.label :contact_email, "Contact Email", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.email_field :contact_email, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :contact_phone, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Contact Phone <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.telephone_field :contact_phone, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :headquarters_location, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Headquarters Location <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.text_field :headquarters_location, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors", placeholder: "City, State/Country" %>
          </div>

          <div>
            <%= form.label :company_size, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Company Size <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :company_size, 
                  options_for_select([
                    ['1-10 employees', '1-10'],
                    ['11-50 employees', '11-50'],
                    ['51-200 employees', '51-200'],
                    ['201-500 employees', '201-500'],
                    ['501-1000 employees', '501-1000'],
                    ['1000+ employees', '1000+']
                  ], @company_profile.company_size),
                  { include_blank: 'Select company size' },
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm transition-colors" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>

          <div>
            <%= form.label :linkedin_url, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              LinkedIn URL <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.url_field :linkedin_url, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors", placeholder: "https://linkedin.com/company/..." %>
          </div>
        </div>

        <div class="flex items-center">
          <%= form.check_box :published, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" %>
          <%= form.label :published, class: "ml-2 block text-sm text-gray-900 dark:text-gray-100" do %>
            Publish company profile (make it visible to government users) <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
          <% end %>
        </div>

        <div class="flex justify-end space-x-3">
          <%= link_to "Cancel", vendors_root_path, class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          <%= form.submit "Create Company Profile", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      <% end %>
    </div>
  </div>
</div>