<% content_for :page_title, "Company Profile" %>

<div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition-colors">
  <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
    <div class="flex items-center space-x-4">
      <!-- Company Logo -->
      <div class="w-20 h-12 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 flex-shrink-0 flex items-center justify-center">
        <% if @company_profile.logo.attached? %>
          <%= image_tag @company_profile.logo_medium, 
              class: "max-w-full max-h-full object-contain", 
              alt: "#{@company_profile.company_name} logo" %>
        <% else %>
          <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        <% end %>
      </div>
      <div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100"><%= @company_profile.company_name %></h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
          Company profile details and information
        </p>
      </div>
    </div>
    <div class="flex space-x-3">
      <%= link_to "Edit Profile", edit_vendors_company_profile_path, 
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
      <% if @company_profile.published? %>
        <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
          Published
        </span>
      <% else %>
        <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
          Draft
        </span>
      <% end %>
    </div>
  </div>
  
  <div class="border-t border-gray-200 dark:border-gray-700">
    <div class="p-6 space-y-6">
      <!-- Company Information -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Company Information</h3>
        <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company Name</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <%= @company_profile.company_name %>
              <% if @company_profile.linkedin_url.present? %>
                <%= link_to @company_profile.linkedin_url, target: '_blank', class: "ml-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" do %>
                  <svg class="w-4 h-4 inline-block" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                <% end %>
              <% end %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Website</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <% if @company_profile.website.present? %>
                <%= link_to @company_profile.website, @company_profile.website, target: '_blank', class: "text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" %>
              <% else %>
                <span class="text-gray-400 dark:text-gray-500">No website yet</span>
              <% end %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company Size</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <%= @company_profile.company_size || "Not specified" %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Headquarters</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <%= @company_profile.headquarters_location || "Not specified" %>
            </dd>
          </div>
        </dl>
      </div>

      <!-- Contact Information -->
      <% if @company_profile.contact_email.present? || @company_profile.contact_phone.present? %>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Contact Information</h3>
          <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <% if @company_profile.contact_email.present? %>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                  <%= mail_to @company_profile.contact_email, class: "text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" %>
                </dd>
              </div>
            <% end %>
            <% if @company_profile.contact_phone.present? %>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                  <%= @company_profile.contact_phone %>
                </dd>
              </div>
            <% end %>
          </dl>
        </div>
      <% end %>

      <!-- Description -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Description</h3>
        <div class="text-sm text-gray-900 dark:text-gray-100">
          <% if @company_profile.description.present? %>
            <%= simple_format(@company_profile.description) %>
          <% else %>
            <span class="text-gray-400 dark:text-gray-500">No description yet</span>
          <% end %>
        </div>
      </div>
      
    </div>
  </div>
</div>