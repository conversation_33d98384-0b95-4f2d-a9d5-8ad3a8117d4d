<% content_for :page_title, "Edit Company Profile" %>

<div class="max-w-3xl mx-auto">
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">Edit Company Profile</h3>
      
      <%= form_with model: [@company_profile], url: vendors_company_profile_path, method: :patch, local: true, class: "space-y-6" do |form| %>
        <% if @company_profile.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were <%= pluralize(@company_profile.errors.count, "error") %> with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul role="list" class="list-disc space-y-1 pl-5">
                    <% @company_profile.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div class="sm:col-span-2">
            <%= form.label :company_name, "Company Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :company_name, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div class="sm:col-span-2">
            <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Description <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.rich_text_area :description, class: "mt-1 block w-full" %>
          </div>

          <div>
            <%= form.label :website, "Website URL", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.url_field :website, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors", placeholder: "https://www.example.com" %>
          </div>

          <div>
            <%= form.label :contact_email, "Contact Email", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.email_field :contact_email, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :contact_phone, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Contact Phone <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.telephone_field :contact_phone, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :headquarters_location, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Headquarters Location <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.text_field :headquarters_location, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors", placeholder: "City, State/Country" %>
          </div>

          <div>
            <%= form.label :company_size, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Company Size <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :company_size, 
                  options_for_select([
                    ['1-10 employees', '1-10'],
                    ['11-50 employees', '11-50'],
                    ['51-200 employees', '51-200'],
                    ['201-500 employees', '201-500'],
                    ['501-1000 employees', '501-1000'],
                    ['1000+ employees', '1000+']
                  ], @company_profile.company_size),
                  { include_blank: 'Select company size' },
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm transition-colors" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>


          <div>
            <%= form.label :linkedin_url, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              LinkedIn URL <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.url_field :linkedin_url, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors", placeholder: "https://linkedin.com/company/..." %>
          </div>
        </div>

        <div class="flex items-center">
          <%= form.check_box :published, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" %>
          <%= form.label :published, "Publish company profile (make it visible to government users)", class: "ml-2 block text-sm text-gray-900 dark:text-gray-100" %>
        </div>

        <div class="flex justify-end space-x-3">
          <%= link_to "Cancel", vendors_company_profile_path, class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
          <%= form.submit "Update Company Profile", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Company Logo Form -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mt-6 transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">Company Logo</h3>
      
      <%= form_with model: @company_profile, url: update_logo_vendors_company_profile_path, method: :patch, local: true, multipart: true, class: "space-y-6" do |form| %>
        <% if @company_profile.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were <%= pluralize(@company_profile.errors.count, "error") %> with your logo upload:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul role="list" class="list-disc space-y-1 pl-5">
                    <% @company_profile.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="flex items-start space-x-6">
          <!-- Current Logo -->
          <div class="flex-shrink-0">
            <div class="w-24 h-16 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 flex items-center justify-center">
              <% if @company_profile.logo.attached? %>
                <%= image_tag @company_profile.logo_small, 
                    class: "max-w-full max-h-full object-contain", 
                    alt: "#{@company_profile.company_name} logo" %>
              <% else %>
                <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                  <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              <% end %>
            </div>
          </div>

          <!-- Upload Controls -->
          <div class="flex-1 min-w-0">
            <div class="mb-4">
              <%= form.label :logo, "Choose new logo", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              
              <div class="flex items-center space-x-4">
                <div class="flex-1">
                  <%= form.file_field :logo, 
                      accept: "image/jpeg,image/jpg,image/png,image/gif,image/webp,image/svg+xml",
                      class: "block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/50 dark:file:text-blue-300 dark:hover:file:bg-blue-900/70",
                      id: "company_logo_input" %>
                </div>
                
                <!-- Remove Logo Button -->
                <% if @company_profile.logo.attached? %>
                  <%= link_to "Remove", remove_logo_vendors_company_profile_path, 
                      method: :delete, 
                      data: { confirm: "Are you sure you want to remove your company logo?" },
                      class: "px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300" %>
                <% end %>
              </div>
            </div>

            <!-- File Requirements -->
            <div class="text-sm text-gray-500 dark:text-gray-400 mb-4">
              <p>• Maximum file size: 5MB</p>
              <p>• Accepted formats: JPEG, PNG, GIF, WebP, SVG</p>
              <p>• Recommended: Minimum 200px width (height will scale proportionally)</p>
              <p>• Logo will be optimized to WebP format for faster loading</p>
              <p>• Aspect ratio will be preserved - works with both square and horizontal logos</p>
            </div>

            <!-- Preview Area -->
            <div id="logo-preview" class="hidden">
              <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 mb-4">
                <div class="flex items-center space-x-4">
                  <div class="w-16 h-12 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 flex items-center justify-center">
                    <img id="preview-logo" class="max-w-full max-h-full object-contain" alt="Preview" />
                  </div>
                  <div class="flex-1">
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                      <span class="font-medium">New logo preview</span>
                      <% if @company_profile.logo.attached? %>
                        <br><span class="text-xs text-gray-500 dark:text-gray-400">This will replace your current logo</span>
                      <% end %>
                    </p>
                    <p id="logo-file-info" class="text-xs text-gray-500 dark:text-gray-400 mt-1"></p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Upload Status Message -->
            <div id="upload-status" class="hidden mb-4 p-3 rounded-md">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg id="upload-spinner" class="animate-spin h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p id="upload-message" class="text-sm font-medium text-blue-800 dark:text-blue-200">Uploading logo...</p>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3">
              <%= link_to "Cancel", vendors_company_profile_path, class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
              <%= form.submit "Update Logo", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors", id: "upload-logo-btn", disabled: true %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const logoInput = document.getElementById('company_logo_input');
  const previewDiv = document.getElementById('logo-preview');
  const previewImage = document.getElementById('preview-logo');
  const fileInfo = document.getElementById('logo-file-info');
  const uploadBtn = document.getElementById('upload-logo-btn');
  const uploadStatus = document.getElementById('upload-status');
  const uploadMessage = document.getElementById('upload-message');
  const logoForm = uploadBtn.closest('form');
  
  if (logoInput) {
    logoInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      
      if (file) {
        // Validate file size
        if (file.size > 5 * 1024 * 1024) {
          alert('File size must be less than 5MB');
          logoInput.value = '';
          previewDiv.classList.add('hidden');
          uploadBtn.disabled = true;
          return;
        }
        
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
        if (!allowedTypes.includes(file.type)) {
          alert('Please select a valid image file (JPEG, PNG, GIF, WebP, or SVG)');
          logoInput.value = '';
          previewDiv.classList.add('hidden');
          uploadBtn.disabled = true;
          return;
        }
        
        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
          previewImage.src = e.target.result;
          fileInfo.textContent = `${file.name} (${formatFileSize(file.size)})`;
          previewDiv.classList.remove('hidden');
          uploadBtn.disabled = false;
        };
        reader.readAsDataURL(file);
      } else {
        previewDiv.classList.add('hidden');
        uploadBtn.disabled = true;
      }
    });
  }
  
  // Handle form submission to show upload progress
  if (logoForm) {
    logoForm.addEventListener('submit', function(e) {
      if (!uploadBtn.disabled) {
        // Show upload status
        uploadStatus.classList.remove('hidden');
        uploadStatus.classList.add('bg-blue-50', 'dark:bg-blue-900/50', 'border', 'border-blue-200', 'dark:border-blue-800');
        uploadMessage.textContent = 'Uploading logo...';
        
        // Update button text and disable it
        uploadBtn.textContent = 'Uploading...';
        uploadBtn.disabled = true;
        
        // Disable file input
        logoInput.disabled = true;
      }
    });
  }
  
  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
});
</script>