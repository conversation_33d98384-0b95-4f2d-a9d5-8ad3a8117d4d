<%= form_with model: [@product, @case_study], url: form_url, local: true, multipart: true, class: "space-y-6" do |form| %>
  <% if @case_study.errors.any? %>
    <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were errors with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul class="list-disc pl-5 space-y-1">
              <% @case_study.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div>
    <%= form.label :title, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.text_field :title, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "Enter case study title" %>
  </div>

  <div>
    <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.text_area :description, 
        rows: 4,
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "Describe this case study..." %>
  </div>

  <!-- Current PDF Preview -->
  <% if @case_study.upload.attached? %>
    <div>
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Current PDF</label>
      <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
              <%= @case_study.upload.filename %>
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              <%= @case_study.upload.content_type %> • <%= number_to_human_size(@case_study.upload.byte_size) %>
            </p>
          </div>
          <div class="flex-shrink-0">
            <%= link_to 'Preview', url_for(@case_study.upload), target: '_blank', class: 'text-indigo-600 hover:text-indigo-500 text-sm font-medium' %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div>
    <%= form.label :upload, @case_study.upload.attached? ? "Replace PDF File (Optional)" : "PDF File", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <div class="mt-1">
      <%= form.file_field :upload, 
          accept: "application/pdf,.pdf",
          class: "block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100 dark:file:bg-indigo-900/50 dark:file:text-indigo-300 dark:hover:file:bg-indigo-900/70 file:cursor-pointer border border-gray-300 dark:border-gray-600 rounded-md focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700" %>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        <% if @case_study.upload.attached? %>
          Leave empty to keep the current PDF file. Upload a new PDF to replace it. Maximum file size: 20MB.
        <% else %>
          Upload a PDF file. Maximum file size: 20MB.
        <% end %>
      </p>
    </div>
  </div>

  <!-- Publishing Options Section -->
  <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
    <div class="flex items-center">
      <%= form.check_box :published, 
          class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" %>
      <%= form.label :published, "Publish this case study", class: "ml-2 block text-sm text-gray-900 dark:text-gray-100" %>
      <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">(You can change this later)</p>
    </div>
  </div>

  <div class="flex justify-between pt-5">
    <div class="flex space-x-3">
      <%= form.submit submit_text, 
          class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
      <%= link_to 'Cancel', vendors_product_case_studies_path(@product), 
          class: "inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
    </div>
  </div>
<% end %>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const fileInput = document.querySelector('input[type="file"]');
  const submitBtn = document.querySelector('input[type="submit"]');
  let originalSubmitText = submitBtn.value;
  
  // Handle file input change
  fileInput.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      // Check file size (20MB limit)
      const maxSize = 20 * 1024 * 1024; // 20MB in bytes
      if (file.size > maxSize) {
        alert('File size must be less than 20MB');
        fileInput.value = '';
        return;
      }
      
      // Check file type (PDF only)
      if (file.type !== 'application/pdf') {
        alert('Please select a PDF file');
        fileInput.value = '';
        return;
      }
    }
  });
  
  // Handle form submission
  const form = document.querySelector('form');
  form.addEventListener('submit', function() {
    submitBtn.disabled = true;
    submitBtn.value = 'Uploading...';
    
    // Re-enable form if there's an error
    setTimeout(function() {
      if (submitBtn.disabled) {
        submitBtn.disabled = false;
        submitBtn.value = originalSubmitText;
      }
    }, 30000); // 30 second timeout
  });
});
</script>