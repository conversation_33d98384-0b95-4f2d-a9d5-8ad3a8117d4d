<div class="max-w-7xl mx-auto">
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="min-w-0 flex-1">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl sm:tracking-tight">
        <%= @product_content.title %>
      </h2>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0">
      <div class="flex space-x-2">
        <%= link_to 'Edit Content', edit_vendors_product_product_content_path(@product, @product_content), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        <%= link_to 'All Content', vendors_product_product_contents_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <!-- Content Details -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Content Information</h3>
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Title</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_content.title %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <%= simple_format(@product_content.description) %>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Position</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_content.position %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @product_content.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                  <%= @product_content.published? ? 'Published' : 'Draft' %>
                </span>
              </dd>
            </div>
          </dl>
        </div>

        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">File Details</h3>
          <% if @product_content.file.attached? %>
            <dl class="space-y-4">
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Filename</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_content.file.filename %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Content Type</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_content.file.content_type %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Size</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= number_to_human_size(@product_content.file.byte_size) %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Type</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 capitalize"><%= @product_content.file_type %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Uploaded</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= time_ago_in_words(@product_content.created_at) %> ago</dd>
              </div>
            </dl>
          <% else %>
            <p class="text-sm text-gray-500 dark:text-gray-400">No file uploaded.</p>
          <% end %>
        </div>
      </div>

      <!-- File Viewer Section -->
      <% if @product_content.file.attached? %>
        <div class="mt-8">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">File Preview</h3>
          <% if @product_content.file_type == 'pdf' %>
            <!-- PDF Viewer -->
            <div class="aspect-[8.5/11] bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
              <iframe 
                src="<%= url_for(@product_content.file) %>#toolbar=0&navpanes=0&scrollbar=0"
                class="w-full h-full border-0"
                title="<%= @product_content.title %> PDF">
              </iframe>
            </div>
          <% elsif @product_content.file_type == 'video' %>
            <!-- Video Player -->
            <div class="aspect-video bg-black rounded-lg overflow-hidden">
              <video 
                controls 
                controlsList="nodownload"
                class="w-full h-full object-contain"
                preload="metadata"
                poster="">
                <source src="<%= url_for(@product_content.file) %>" type="<%= @product_content.file.content_type %>">
                Your browser does not support the video tag.
              </video>
            </div>
          <% else %>
            <!-- Unknown file type -->
            <div class="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Unsupported file type</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Download the file to view it.</p>
              </div>
            </div>
          <% end %>
          
          <div class="mt-4 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <%= @product_content.file_type == 'pdf' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' %>">
                <% if @product_content.file_type == 'pdf' %>
                  <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                  </svg>
                <% else %>
                  <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                  </svg>
                <% end %>
                <%= @product_content.file_type&.upcase %> • <%= number_to_human_size(@product_content.file.byte_size) %>
              </span>
            </div>
            <div class="flex space-x-2">
              <%= link_to 'Download', url_for(@product_content.file), 
                  target: '_blank', 
                  download: @product_content.file.filename,
                  class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
              <%= link_to 'Open in New Tab', url_for(@product_content.file), 
                  target: '_blank',
                  class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            </div>
          </div>
        </div>
      <% else %>
        <div class="mt-8">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">File Preview</h3>
          <div class="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No file uploaded</h3>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Upload a file to preview it here.</p>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>


</div>