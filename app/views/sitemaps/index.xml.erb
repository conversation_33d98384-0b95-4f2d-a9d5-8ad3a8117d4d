<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
  
  <!-- Static Pages -->
  <% @static_pages.each do |page| %>
    <url>
      <loc><%= page[:url] %></loc>
      <changefreq><%= page[:changefreq] %></changefreq>
      <priority><%= page[:priority] %></priority>
      <lastmod><%= Time.current.strftime('%Y-%m-%d') %></lastmod>
    </url>
  <% end %>

  <!-- Products -->
  <% @products.each do |product| %>
    <url>
      <loc><%= marketplace_product_url(product) %></loc>
      <changefreq>weekly</changefreq>
      <priority>0.6</priority>
      <lastmod><%= product.updated_at.strftime('%Y-%m-%d') %></lastmod>
    </url>
  <% end %>

  <!-- Companies -->
  <% @companies.each do |company| %>
    <url>
      <loc><%= agency_company_url(company) %></loc>
      <changefreq>weekly</changefreq>
      <priority>0.5</priority>
      <lastmod><%= company.updated_at.strftime('%Y-%m-%d') %></lastmod>
    </url>
  <% end %>

</urlset>