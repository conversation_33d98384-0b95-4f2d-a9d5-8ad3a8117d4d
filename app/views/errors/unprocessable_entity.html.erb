<% content_for :page_title, "Request Error" %>

<div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8 text-center">
    <!-- Error Icon -->
    <div>
      <div class="mx-auto h-24 w-24 text-yellow-500 dark:text-yellow-400">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
      </div>
    </div>

    <!-- Error Content -->
    <div>
      <h2 class="text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4"><%= @error_code %></h2>
      <h3 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4"><%= @error_title %></h3>
      <p class="text-gray-600 dark:text-gray-400 mb-8"><%= @error_message %></p>
    </div>

    <!-- Help Section -->
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-8">
      <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">Common Causes</h4>
      <ul class="text-sm text-yellow-700 dark:text-yellow-300 text-left space-y-1">
        <li>• Invalid or missing required information</li>
        <li>• Form data that doesn't meet validation requirements</li>
        <li>• Expired or invalid security tokens</li>
        <li>• File uploads that are too large or invalid format</li>
      </ul>
    </div>

    <!-- Action Buttons -->
    <div class="space-y-4">
      <button onclick="history.back()" 
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 dark:focus:ring-offset-gray-900 transition-colors">
        Go Back and Try Again
      </button>
      
      <%= link_to "Go to Marketplace", marketplace_path, 
          class: "w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 transition-colors" %>
      
      <%= link_to "Back to Home", root_path, 
          class: "w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 transition-colors" %>
    </div>

    <!-- Contact Section -->
    <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
      <h4 class="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">Need Assistance?</h4>
      <p class="text-sm text-blue-700 dark:text-blue-300">
        If you continue to experience issues, our support team is here to help:
        <a href="mailto:<EMAIL>" class="underline font-medium hover:text-blue-600 dark:hover:text-blue-200 transition-colors"><EMAIL></a>
      </p>
    </div>
  </div>
</div>