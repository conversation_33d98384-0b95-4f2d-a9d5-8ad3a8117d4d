<%# 
  Reusable delete resource partial with modal confirmation
  
  Required locals:
  - resource: The resource to delete (e.g., @product_video, @case_study, @product_content)
  - delete_url: The URL to send DELETE request to
  - redirect_url: Where to redirect after successful deletion
  - resource_type: Display name (e.g., "Video", "Case Study", "Content")
  
  Optional locals:
  - item_name: Custom name for the resource (automatically tries title, then name, then class name)
  - description: Custom description text
%>

<div data-controller="delete-resource"
     data-delete-resource-delete-url-value="<%= delete_url %>"
     data-delete-resource-redirect-url-value="<%= redirect_url %>"
     data-delete-resource-item-name-value="<%= local_assigns[:item_name] || resource.try(:title) || resource.try(:name) || resource.class.name %>"
     data-delete-resource-resource-type-value="<%= resource_type %>">

  <!-- Danger Zone -->
  <div class="mt-8">
    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Danger Zone</h3>
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
      <div class="px-4 py-5 sm:p-6">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Once you delete this <%= resource_type.downcase %>, there's no going back. This action cannot be undone.
        </p>
        <button type="button" 
                data-action="click->delete-resource#showModal"
                class="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Delete <%= resource_type %>
        </button>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div data-delete-resource-target="modal" 
       data-action="click->delete-resource#clickOutside"
       class="fixed inset-0 z-50 overflow-y-auto hidden" 
       aria-labelledby="modal-title" 
       role="dialog" 
       aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="modal-backdrop fixed inset-0 bg-gray-500/75 transition-opacity" aria-hidden="true"></div>
      
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
      
      <div class="relative inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
        <div class="sm:flex sm:items-start">
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/50 sm:mx-0 sm:h-10 sm:w-10">
            <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
              Delete <%= resource_type %>
            </h3>
            <div class="mt-2">
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Are you sure you want to delete "<span data-delete-resource-target="itemName" class="font-medium text-gray-900 dark:text-gray-100"><%= local_assigns[:item_name] || resource.try(:title) || resource.try(:name) || resource.class.name %></span>"? This action cannot be undone and will permanently remove the <%= resource_type.downcase %>.
              </p>
            </div>
          </div>
        </div>
        
        <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
          <%= link_to delete_url, 
                  method: :delete,
                  data: { 
                    turbo_method: :delete,
                    delete_resource_target: "confirmButton"
                  },
                  class: "w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm transition-colors" do %>
            Delete <%= resource_type %>
          <% end %>
          <button type="button" 
                  data-delete-resource-target="cancelButton"
                  data-action="click->delete-resource#hideModal"
                  class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 sm:mt-0 sm:w-auto sm:text-sm transition-colors">
            Cancel
          </button>
        </div>
      </div>
    </div>

    
  </div>
</div>
  
  