<% content_for :page_title, "Account Confirmation - Platia" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8 transition-colors">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <div class="text-center">
      <div class="mx-auto h-16 w-16 bg-<%= @confirmation_success ? 'green' : 'blue' %>-100 dark:bg-<%= @confirmation_success ? 'green' : 'blue' %>-900 rounded-full flex items-center justify-center mb-6">
        <% if @confirmation_success %>
          <svg class="h-8 w-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        <% else %>
          <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        <% end %>
      </div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
        <%= @confirmation_success ? "Account Confirmed!" : "Account Already Confirmed" %>
      </h1>
      <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
        <%= @confirmation_success ? "Your account has been successfully confirmed." : "This account has already been confirmed." %>
      </p>
    </div>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
    <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10 transition-colors">
      <div class="space-y-6">
        <!-- Account Status -->
        <% if @account.approved? %>
          <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-800 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400 dark:text-green-300" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Account Approved</h3>
                <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                  <p>Great news! Your account has been approved and is ready to use.</p>
                </div>
              </div>
            </div>
          </div>
        <% else %>
          <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400 dark:text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Pending Approval</h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                  <p>Your account is confirmed but still pending approval. Our team will review your registration and notify you once it's approved.</p>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Action buttons -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <!-- Home button -->
          <div>
            <%= link_to root_path, 
                class: "w-full flex justify-center items-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" do %>
              <svg class="h-5 w-5 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
              </svg>
              Home
            <% end %>
          </div>
          
          <!-- Conditional button based on approval status -->
          <div>
            <% if @account.approved? %>
              <%= link_to @account.vendor? ? vendors_root_path : agencies_root_path, 
                  class: "w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" do %>
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                Go to Dashboard
              <% end %>
            <% else %>
              <%= link_to marketplace_path, 
                  class: "w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" do %>
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                Browse Marketplace
              <% end %>
            <% end %>
          </div>
        </div>

        <!-- Conditional messaging based on approval status -->
        <% if @account.approved? %>
          <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-800 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400 dark:text-green-300" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Ready to get started!</h3>
                <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                  <p>Your account is fully approved and ready to use. Access your dashboard to start managing your <%= @account.account_type %> account.</p>
                </div>
              </div>
            </div>
          </div>
        <% else %>
          <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-800 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Explore while you wait</h3>
                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                  <p>Your account is confirmed and awaiting approval. Browse our marketplace to discover technology solutions and companies!</p>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Contact info -->
        <div class="text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Questions? Contact us at 
            <a href="mailto:<EMAIL>" class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
              <EMAIL>
            </a>
          </p>
        </div>

        <!-- Sign in link -->
        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Ready to sign in?
            </p>
            <%= link_to "Sign In", new_session_path,
                class: "w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>