<% content_for :page_title, "Account Pending Approval - Platia" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8 transition-colors">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <!-- Flash messages -->
    <% if notice %>
      <div class="mb-6 rounded-md bg-green-50 dark:bg-green-900/50 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400 dark:text-green-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-green-800 dark:text-green-200"><%= notice %></p>
          </div>
        </div>
      </div>
    <% end %>

    <% if alert %>
      <div class="mb-6 rounded-md bg-red-50 dark:bg-red-900/50 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400 dark:text-red-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-red-800 dark:text-red-200"><%= alert %></p>
          </div>
        </div>
      </div>
    <% end %>

    <div class="text-center">
      <div class="mx-auto h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-6">
        <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">Account Pending Approval</h1>
      <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
        Thank you for signing up! Your account is being reviewed by our team.
      </p>
    </div>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
    <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10 transition-colors">
      <div class="space-y-6">
        <!-- Account Status -->
        <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400 dark:text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Approval Required</h3>
              <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                <p>Your account is currently pending approval. Our team will review your registration and notify you once it's approved.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- What happens next -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">What happens next?</h3>
          <div class="space-y-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-sm font-medium">1</div>
              </div>
              <div class="ml-3">
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  <strong>Email Confirmation:</strong> Check your email and click the confirmation link (if required).
                </p>
              </div>
            </div>
            
            <div class="flex">
              <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-sm font-medium">2</div>
              </div>
              <div class="ml-3">
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  <strong>Admin Review:</strong> Our team will review your account within 1-2 business days.
                </p>
              </div>
            </div>
            
            <div class="flex">
              <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-sm font-medium">3</div>
              </div>
              <div class="ml-3">
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  <strong>Approval Notification:</strong> You'll receive an email when your account is approved.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Resend Confirmation (if needed) -->
        <% if current_user && @unconfirmed_accounts.any? %>
          <div class="bg-orange-50 dark:bg-orange-900 border border-orange-200 dark:border-orange-800 rounded-md p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-orange-400 dark:text-orange-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3 flex-1">
                <h3 class="text-sm font-medium text-orange-800 dark:text-orange-200">Email Confirmation Required</h3>
                <div class="mt-2 text-sm text-orange-700 dark:text-orange-300">
                  <p>You have unconfirmed accounts. Please check your email for confirmation links.</p>
                </div>
                <div class="mt-4">
                  <% @unconfirmed_accounts.each do |account| %>
                    <%= form_with url: account_confirmations_path, method: :post, local: true, class: "inline-block mr-2 mb-2" do |form| %>
                      <%= form.hidden_field :account_id, value: account.id %>
                      <%= form.submit "Resend Confirmation for #{account.name}", 
                          class: "inline-flex items-center px-3 py-2 border border-orange-300 dark:border-orange-600 rounded-md shadow-sm text-sm font-medium text-orange-700 dark:text-orange-300 bg-white dark:bg-gray-700 hover:bg-orange-50 dark:hover:bg-orange-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors" %>
                    <% end %>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Action buttons -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <!-- Home button -->
          <div>
            <%= link_to root_path, 
                class: "w-full flex justify-center items-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" do %>
              <svg class="h-5 w-5 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
              </svg>
              Home
            <% end %>
          </div>
          
          <!-- Marketplace button -->
          <div>
            <%= link_to marketplace_path, 
                class: "w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" do %>
              <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
              </svg>
              Browse Marketplace
            <% end %>
          </div>
        </div>

        <!-- Browse explanation -->
        <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-800 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Explore while you wait</h3>
              <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                <p>Browse our marketplace to discover technology solutions and companies. No account needed to explore!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact info -->
        <div class="text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Questions? Contact us at 
            <a href="mailto:<EMAIL>" class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
              <EMAIL>
            </a>
          </p>
        </div>

        <!-- Try signing in again -->
        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Already approved? Try signing in again.
            </p>
            <%= link_to "Sign In", new_session_path,
                class: "w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>