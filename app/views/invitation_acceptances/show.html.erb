<% content_for :page_title, "Team Invitation" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8 transition-colors">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <div class="text-center">
      <div class="mx-auto h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-6">
        <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">Team Invitation</h1>
      <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
        You've been invited to join a team on Platia
      </p>
    </div>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
    <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10 transition-colors">
      <div class="space-y-6">
        <!-- Invitation Details -->
        <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-800 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Invitation Details</h3>
              <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                <p><strong><%= @invitation.invited_by.full_name %></strong> has invited you to join <strong><%= @invitation.account.name %></strong> as a <strong><%= @invitation.role.capitalize %></strong>.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Organization Info -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Organization Information</h3>
          <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Organization</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @invitation.account.name %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Type</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @invitation.account.vendor? ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200' : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' %>">
                  <%= @invitation.account.account_type.capitalize %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Your Role</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @invitation.role == 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' %>">
                  <%= @invitation.role.capitalize %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Invited by</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @invitation.invited_by.full_name %></dd>
            </div>
          </dl>
        </div>

        <!-- Personal Message -->
        <% if @invitation.message.present? %>
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Personal Message</h3>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-md p-4">
              <p class="text-sm text-gray-700 dark:text-gray-300"><%= simple_format(@invitation.message) %></p>
            </div>
          </div>
        <% end %>

        <!-- Role Permissions -->
        <% if @invitation.role == "admin" %>
          <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400 dark:text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Admin Privileges</h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                  <p>As an admin, you'll be able to:</p>
                  <ul class="list-disc list-inside mt-1">
                    <li>Manage team members and send invitations</li>
                    <li>Edit account settings and information</li>
                    <li>Access advanced features and reports</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Expiry Warning -->
        <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400 dark:text-red-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Time Sensitive</h3>
              <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>This invitation expires on <strong><%= @invitation.expires_at.strftime("%B %d, %Y at %I:%M %p") %></strong></p>
              </div>
            </div>
          </div>
        </div>

        <!-- User Account Section -->
        <% if @existing_user %>
          <!-- User already exists - show sign in prompt -->
          <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-800 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Account Found</h3>
                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                  <p>An account with this email already exists. Please sign in to accept the invitation.</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Action buttons for existing user -->
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <%= form_with url: accept_invitation_path(@invitation.token), method: :post, local: true, class: "w-full" do |form| %>
              <%= form.submit "Sign In to Accept", 
                  class: "w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800 transition-colors" %>
            <% end %>
            
            <%= form_with url: decline_invitation_path(@invitation.token), method: :post, local: true, class: "w-full" do |form| %>
              <%= form.submit "Decline", 
                  class: "w-full flex justify-center items-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
            <% end %>
          </div>
        <% else %>
          <!-- User doesn't exist - show account creation form -->
          <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-800 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400 dark:text-green-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Create Your Account</h3>
                <div class="mt-2 text-sm text-green-700 dark:text-green-300">
                  <p>Complete the form below to create your account and join the team.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- User Creation Form -->
          <%= form_with model: @user, url: accept_invitation_path(@invitation.token), method: :post, local: true, class: "space-y-4" do |form| %>
            <% if @user&.errors&.any? %>
              <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-md p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400 dark:text-red-300" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Please correct the following errors:</h3>
                    <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                      <ul class="list-disc list-inside">
                        <% @user.errors.full_messages.each do |message| %>
                          <li><%= message %></li>
                        <% end %>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>

            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <%= form.label :first_name, "First Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
                <%= form.text_field :first_name, 
                    class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                    required: true %>
              </div>
              <div>
                <%= form.label :last_name, "Last Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
                <%= form.text_field :last_name, 
                    class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                    required: true %>
              </div>
            </div>

            <div>
              <%= form.label :job_title, "Job Title", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
              <%= form.text_field :job_title, 
                  class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                  placeholder: "e.g., Software Engineer, Product Manager" %>
            </div>

            <div>
              <%= form.label :email_address, "Email Address", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
              <%= form.email_field :email_address, 
                  class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors",
                  readonly: true %>
              <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">This email is pre-filled from your invitation and cannot be changed.</p>
            </div>

            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <%= form.label :password, "Password", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
                <%= form.password_field :password, 
                    class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                    required: true %>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Must be at least 8 characters</p>
              </div>
              <div>
                <%= form.label :password_confirmation, "Confirm Password", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
                <%= form.password_field :password_confirmation, 
                    class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                    required: true %>
              </div>
            </div>

            <!-- Action buttons for new user -->
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 pt-4">
              <%= form.submit "Create Account & Join Team", 
                  class: "w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800 transition-colors" %>
              
              <%= form_with url: decline_invitation_path(@invitation.token), method: :post, local: true, class: "w-full" do |decline_form| %>
                <%= decline_form.submit "Decline", 
                    class: "w-full flex justify-center items-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
              <% end %>
            </div>
          <% end %>
        <% end %>

        <!-- Information -->
        <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-800 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Next Steps</h3>
              <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                <p>If you accept this invitation, you'll need to sign in to your existing account or create a new one to complete the process.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact info -->
        <div class="text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Questions about this invitation? Contact 
            <a href="mailto:<%= @invitation.invited_by.email_address %>" class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
              <%= @invitation.invited_by.full_name %>
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>