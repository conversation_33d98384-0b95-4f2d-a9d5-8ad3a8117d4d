<% content_for :page_title, "Set New Password - Platia" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8 transition-colors">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <div class="text-center">
      <h1 class="text-3xl font-bold text-gray-900">Set New Password</h1>
      <p class="mt-2 text-sm text-gray-600">
        Choose a strong password for your account
      </p>
    </div>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
      <!-- Flash Messages -->
      <% if flash[:alert] %>
        <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800"><%= flash[:alert] %></p>
            </div>
          </div>
        </div>
      <% end %>

      <% if flash[:notice] %>
        <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-green-800"><%= flash[:notice] %></p>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Password Form -->
      <%= form_with url: password_path(params[:token]), method: :put, local: true do |form| %>
        <div class="mb-6">
          <%= form.label :password, "New Password", class: "block text-sm font-medium text-gray-700" %>
          <%= form.password_field :password, 
              required: true, 
              autofocus: true,
              autocomplete: "new-password",
              maxlength: 72,
              class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
              placeholder: "Enter new password" %>
          <p class="mt-1 text-xs text-gray-500">
            Must be at least 8 characters with uppercase, lowercase, numbers, and special characters
          </p>
        </div>

        <div class="mb-6">
          <%= form.label :password_confirmation, "Confirm New Password", class: "block text-sm font-medium text-gray-700" %>
          <%= form.password_field :password_confirmation, 
              required: true, 
              autocomplete: "new-password",
              maxlength: 72,
              class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
              placeholder: "Repeat new password" %>
        </div>

        <!-- Password Requirements -->
        <div class="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <h4 class="text-sm font-medium text-blue-900 mb-2">Password Requirements</h4>
          <ul class="text-sm text-blue-700 space-y-1">
            <li>• At least 8 characters long</li>
            <li>• Contains uppercase and lowercase letters</li>
            <li>• Contains at least one number</li>
            <li>• Contains at least one special character</li>
            <li>• Cannot contain common password patterns</li>
          </ul>
        </div>

        <div>
          <%= form.submit "Update Password", 
              class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
        </div>
      <% end %>

      <!-- Back to Sign In -->
      <div class="mt-6 text-center">
        <%= link_to "← Back to Sign In", new_session_path,
            class: "text-sm text-blue-600 hover:text-blue-500 font-medium" %>
      </div>
    </div>
  </div>
</div>
