<% content_for :page_title, "Dashboard" %>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <!-- Interest Statistics -->
  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold text-sm">T</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Interests</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @interest_stats[:total] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold text-sm">P</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Pending</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @interest_stats[:pending] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold text-sm">C</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Contacted</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @interest_stats[:contacted] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold text-sm">Q</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Qualified</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @interest_stats[:qualified] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
  <!-- Recent Interests -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Recent Interests</h3>
      <% if @recent_interests.any? %>
        <div class="flow-root">
          <ul role="list" class="-my-5 divide-y divide-gray-200 dark:divide-gray-700">
            <% @recent_interests.each do |interest| %>
              <li class="py-4">
                <div class="flex items-center space-x-4">
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      <%= interest.product.name %>
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                      <%= interest.account.company_profile&.company_name || interest.account.name %>
                    </p>
                  </div>
                  <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-gray-100">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      <%= case interest.status
                          when 'pending' then 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                          when 'contacted' then 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                          when 'qualified' then 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                          else 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200'
                          end %>">
                      <%= interest.status.humanize %>
                    </span>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
        <div class="mt-6">
          <%= link_to "View all interests", agencies_interests_path, 
              class: "w-full flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" %>
        </div>
      <% else %>
        <p class="text-gray-500 dark:text-gray-400 text-center py-4">No interests yet. Explore the marketplace to find products!</p>
        <div class="mt-4">
          <%= link_to "Browse Products", marketplace_products_path, 
              class: "w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Quick Actions</h3>
      <div class="space-y-4">
        <%= link_to marketplace_path, 
            class: "w-full flex items-center px-4 py-3 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" do %>
          <span class="ml-3">Browse Marketplace</span>
        <% end %>
        
        <%= link_to marketplace_companies_path, 
            class: "w-full flex items-center px-4 py-3 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" do %>
          <span class="ml-3">Find Companies</span>
        <% end %>
        
        <%= link_to marketplace_products_path, 
            class: "w-full flex items-center px-4 py-3 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" do %>
          <span class="ml-3">Search Products</span>
        <% end %>
      </div>
    </div>
  </div>
</div>

<% if @featured_companies.any? || @featured_products.any? %>
  <div class="mt-8">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">Featured</h2>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Featured Companies -->
      <% if @featured_companies.any? %>
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Featured Companies</h3>
          <div class="grid grid-cols-1 gap-4">
            <% @featured_companies.limit(3).each do |company| %>
              <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow bg-white dark:bg-gray-800">
                <h4 class="font-medium text-gray-900 dark:text-gray-100">
                  <%= link_to company.company_name, marketplace_company_path(company), 
                      class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" %>
                </h4>
                <% if company.description.present? %>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    <%= company.description %>
                  </p>
                <% end %>
              </div>
            <% end %>
          </div>
          <%= link_to "View all companies", marketplace_companies_path, 
              class: "mt-4 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" %>
        </div>
      <% end %>

      <!-- Featured Products -->
      <% if @featured_products.any? %>
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Featured Products</h3>
          <div class="grid grid-cols-1 gap-4">
            <% @featured_products.limit(3).each do |product| %>
              <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow bg-white dark:bg-gray-800">
                <h4 class="font-medium text-gray-900 dark:text-gray-100">
                  <%= link_to product.name, marketplace_product_path(product), 
                      class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" %>
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  by <%= product.account.company_profile&.company_name || product.account.name %>
                </p>
                <% if product.description.present? %>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    <%= product.description %>
                  </p>
                <% end %>
              </div>
            <% end %>
          </div>
          <%= link_to "View all products", marketplace_products_path, 
              class: "mt-4 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>