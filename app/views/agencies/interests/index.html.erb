<% content_for :page_title, "My Interests" %>

<div class="sm:flex sm:items-center mb-6">
  <div class="sm:flex-auto">
    <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">My Product Interests</h1>
    <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Track all the products you've expressed interest in and their current status.</p>
  </div>
</div>

<% if @leads.any? %>
  <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md transition-colors">
    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
      <% @leads.each do |lead| %>
        <li>
          <div class="px-4 py-4 sm:px-6">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center">
                  <p class="text-lg font-medium text-indigo-600 dark:text-indigo-400 truncate">
                    <%= link_to lead.product.name, marketplace_product_path(lead.product), 
                        class: "hover:text-indigo-900 dark:hover:text-indigo-300" %>
                  </p>
                  <div class="ml-2 flex-shrink-0 flex">
                    <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      <%= case lead.status
                          when 'pending' then 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                          when 'contacted' then 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                          when 'qualified' then 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                          when 'closed' then 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200'
                          else 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200'
                          end %>">
                      <%= lead.status.humanize %>
                    </p>
                  </div>
                </div>
                <div class="mt-2 sm:flex sm:justify-between">
                  <div class="sm:flex">
                    <p class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <span class="font-medium">Vendor:</span>
                      <span class="ml-1"><%= lead.account.company_profile&.company_name || lead.account.name %></span>
                    </p>
                    <% if lead.timeline.present? %>
                      <p class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0 sm:ml-6">
                        <span class="font-medium">Timeline:</span>
                        <span class="ml-1"><%= lead.timeline.humanize %></span>
                      </p>
                    <% end %>
                  </div>
                  <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0">
                    <p>
                      Submitted <time datetime="<%= lead.created_at.iso8601 %>"><%= lead.created_at.strftime("%B %-d, %Y") %></time>
                    </p>
                  </div>
                </div>
                <% if lead.message.present? %>
                  <div class="mt-3">
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                      <span class="font-medium">Your message:</span> <%= lead.message %>
                    </p>
                  </div>
                <% end %>
              </div>
              <div class="ml-6">
                <%= link_to "View Product", marketplace_product_path(lead.product), 
                    class: "inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900 hover:bg-indigo-200 dark:hover:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors" %>
              </div>
            </div>
            
            <!-- Status Timeline -->
            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex items-center space-x-4 text-sm">
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span class="text-gray-600 dark:text-gray-400">Interest submitted</span>
                </div>
                <% if ['contacted', 'qualified', 'closed'].include?(lead.status) %>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span class="text-gray-600 dark:text-gray-400">Vendor contacted</span>
                  </div>
                <% else %>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                    <span class="text-gray-400 dark:text-gray-500">Waiting for vendor response</span>
                  </div>
                <% end %>
                <% if ['qualified', 'closed'].include?(lead.status) %>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span class="text-gray-600 dark:text-gray-400">Qualified lead</span>
                  </div>
                <% end %>
                <% if lead.status == 'closed' %>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                    <span class="text-gray-600 dark:text-gray-400">Opportunity closed</span>
                  </div>
                <% end %>
              </div>
            </div>

            <!-- Contact Information -->
            <div class="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Contact Name:</span>
                <span class="text-gray-600 dark:text-gray-400 block"><%= lead.contact_name %></span>
              </div>
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Email:</span>
                <span class="text-gray-600 dark:text-gray-400 block"><%= lead.contact_email %></span>
              </div>
              <% if lead.contact_phone.present? %>
                <div>
                  <span class="font-medium text-gray-700 dark:text-gray-300">Phone:</span>
                  <span class="text-gray-600 dark:text-gray-400 block"><%= lead.contact_phone %></span>
                </div>
              <% end %>
            </div>

            <% if lead.budget_range.present? %>
              <div class="mt-3 text-sm">
                <span class="font-medium text-gray-700 dark:text-gray-300">Budget Range:</span>
                <span class="text-gray-600 dark:text-gray-400"><%= lead.budget_range.humanize %></span>
              </div>
            <% end %>
          </div>
        </li>
      <% end %>
    </ul>
  </div>
<% else %>
  <div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No interests yet</h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Start exploring products and express your interest to connect with vendors.</p>
    <div class="mt-6">
      <%= link_to "Browse Products", marketplace_products_path, 
          class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors" %>
    </div>
  </div>
<% end %>