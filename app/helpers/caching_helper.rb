module Caching<PERSON>elper
  # Fragment caching with intelligent cache keys
  def cache_if_cacheable(condition, name = nil, options = {}, &block)
    if condition && cacheable_request?
      cache(build_cache_key(name, options), options, &block)
    else
      yield
    end
  end

  def cache_key_for_product(product)
    [
      product,
      product.company,
      product.updated_at.to_i,
      current_user&.id,
      current_account&.account_type
    ]
  end

  def cache_key_for_company(company)
    [
      company,
      company.updated_at.to_i,
      company.products.maximum(:updated_at)&.to_i,
      current_user&.id,
      current_account&.account_type
    ]
  end

  def cache_key_for_user_dashboard(user)
    [
      'user_dashboard',
      user.id,
      user.updated_at.to_i,
      user.accounts.maximum(:updated_at)&.to_i,
      user.accounts.joins(:company).maximum('companies.updated_at')&.to_i
    ]
  end

  def cache_key_for_marketplace_listing(filters = {})
    [
      'marketplace_listing',
      filters.to_h.sort.to_h,
      Company.maximum(:updated_at)&.to_i,
      Product.maximum(:updated_at)&.to_i,
      current_account&.account_type
    ]
  end

  def cache_key_for_admin_stats
    [
      'admin_stats',
      User.maximum(:updated_at)&.to_i,
      Company.maximum(:updated_at)&.to_i,
      Product.maximum(:updated_at)&.to_i,
      Interest.maximum(:updated_at)&.to_i,
      Date.current
    ]
  end

  private

  def cacheable_request?
    request.get? && !request.xhr? && !user_signed_in_recently?
  end

  def user_signed_in_recently?
    return false unless current_user
    
    # Don't cache for recently signed in users (within 5 minutes)
    current_user.sessions.where('created_at > ?', 5.minutes.ago).exists?
  end

  def build_cache_key(name, options)
    key_parts = [name || "#{controller_name}_#{action_name}"]
    key_parts << options[:version] if options[:version]
    key_parts << I18n.locale
    key_parts << request.variant if request.variant.present?
    key_parts.compact.join('/')
  end
end