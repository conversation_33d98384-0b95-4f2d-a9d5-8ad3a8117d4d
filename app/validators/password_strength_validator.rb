class PasswordStrengthValidator < ActiveModel::EachValidator
  def validate_each(record, attribute, value)
    return if value.blank?

    errors = []
    
    # Minimum length
    if value.length < 8
      errors << "must be at least 8 characters long"
    end
    
    # Maximum length (prevent DoS)
    if value.length > 128
      errors << "must be no longer than 128 characters"
    end
    
    # Character variety requirements
    unless value.match?(/[a-z]/)
      errors << "must contain at least one lowercase letter"
    end
    
    unless value.match?(/[A-Z]/)
      errors << "must contain at least one uppercase letter"
    end
    
    unless value.match?(/[0-9]/)
      errors << "must contain at least one number"
    end
    
    unless value.match?(/[^a-zA-Z0-9]/)
      errors << "must contain at least one special character"
    end
    
    # Common password patterns
    if value.match?(/(.)\1{2,}/)
      errors << "cannot contain repeated characters (e.g., 'aaa')"
    end
    
    # Common weak passwords
    weak_patterns = [
      /password/i,
      /123456/,
      /qwerty/i,
      /admin/i,
      /letmein/i,
      /welcome/i
    ]
    
    weak_patterns.each do |pattern|
      if value.match?(pattern)
        errors << "cannot contain common password patterns"
        break
      end
    end
    
    # Sequential characters
    if has_sequential_chars?(value)
      errors << "cannot contain sequential characters (e.g., '123', 'abc')"
    end
    
    errors.each do |error|
      record.errors.add(attribute, error)
    end
  end
  
  private
  
  def has_sequential_chars?(password)
    # Check for 3+ sequential ascending characters
    password.downcase.each_char.each_cons(3) do |chars|
      if chars.map(&:ord).each_cons(2).all? { |a, b| b == a + 1 }
        return true
      end
    end
    
    # Check for 3+ sequential descending characters  
    password.downcase.each_char.each_cons(3) do |chars|
      if chars.map(&:ord).each_cons(2).all? { |a, b| b == a - 1 }
        return true
      end
    end
    
    false
  end
end