class SecurityLogger
  def self.log_failed_login(email_address, ip_address, user_agent)
    Rails.logger.warn({
      event: 'failed_login_attempt',
      email_address: email_address,
      ip_address: ip_address,
      user_agent: user_agent,
      timestamp: Time.current
    }.to_json)
  end

  def self.log_successful_login(user, ip_address, user_agent)
    Rails.logger.info({
      event: 'successful_login',
      user_id: user.id,
      email_address: user.email_address,
      ip_address: ip_address,
      user_agent: user_agent,
      timestamp: Time.current
    }.to_json)
  end

  def self.log_password_change(user, ip_address)
    Rails.logger.info({
      event: 'password_change',
      user_id: user.id,
      email_address: user.email_address,
      ip_address: ip_address,
      timestamp: Time.current
    }.to_json)
  end

  def self.log_admin_action(admin_user, action, target_user, ip_address)
    Rails.logger.warn({
      event: 'admin_action',
      admin_user_id: admin_user.id,
      admin_email: admin_user.email_address,
      action: action,
      target_user_id: target_user.id,
      target_email: target_user.email_address,
      ip_address: ip_address,
      timestamp: Time.current
    }.to_json)
  end

  def self.log_suspicious_activity(event_type, details, ip_address, user_agent)
    Rails.logger.error({
      event: 'suspicious_activity',
      event_type: event_type,
      details: details,
      ip_address: ip_address,
      user_agent: user_agent,
      timestamp: Time.current
    }.to_json)
  end

  def self.log_csrf_failure(ip_address, user_agent, referer)
    Rails.logger.error({
      event: 'csrf_failure',
      ip_address: ip_address,
      user_agent: user_agent,
      referer: referer,
      timestamp: Time.current
    }.to_json)
  end

  def self.log_unauthorized_access(path, ip_address, user_agent, user_id = nil)
    Rails.logger.warn({
      event: 'unauthorized_access_attempt',
      path: path,
      user_id: user_id,
      ip_address: ip_address,
      user_agent: user_agent,
      timestamp: Time.current
    }.to_json)
  end
end