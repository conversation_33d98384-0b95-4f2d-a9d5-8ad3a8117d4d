@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

.dark .trix-button--icon {
  -webkit-filter: invert(100%);
}

/* Form controls styling for light/dark mode */
input[type="checkbox"], input[type="radio"] {
  @apply appearance-none border-2 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 transition-colors;
}

input[type="checkbox"] {
  @apply rounded;
}

input[type="radio"] {
  @apply rounded-full;
}

input[type="checkbox"]:checked, input[type="radio"]:checked {
  @apply border-blue-600 bg-blue-600;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m5.707 7.293-2.414-2.414a1 1 0 0 0-1.414 1.414l3 3a1 1 0 0 0 1.414 0l7-7A1 1 0 0 0 11.879 0.707L5.707 7.293z'/%3e%3c/svg%3e");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

input[type="radio"]:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

input[type="checkbox"]:focus, input[type="radio"]:focus {
  @apply ring-2 ring-blue-500 ring-offset-2 ring-offset-white dark:ring-offset-gray-800;
}

/* Scrollbar styling for light/dark mode */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgb(209, 213, 219) transparent;
}

.dark .scrollbar-thin {
  scrollbar-color: rgb(75, 85, 99) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

.pagy {
  @apply flex space-x-1 font-semibold text-sm text-gray-500 dark:text-gray-400;
  a:not(.gap) {
    @apply block rounded-lg px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
    &:hover {
      @apply bg-gray-300 dark:bg-gray-600;
    }
    &:not([href]) { /* disabled links */
      @apply text-gray-300 dark:text-gray-600 bg-gray-100 dark:bg-gray-800 cursor-default;
    }
    &.current {
      @apply text-white bg-gray-400 dark:bg-gray-500;
    }
  }
  label {
    @apply inline-block whitespace-nowrap bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg px-3 py-0.5;
    input {
      @apply bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100 border-none rounded-md;
    }
  }
}