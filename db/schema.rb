# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_16_000002) do
  create_table "account_users", force: :cascade do |t|
    t.integer "account_id", null: false
    t.integer "user_id", null: false
    t.string "role"
    t.datetime "joined_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_account_users_on_account_id"
    t.index ["user_id"], name: "index_account_users_on_user_id"
  end

  create_table "accounts", force: :cascade do |t|
    t.string "name"
    t.string "account_type"
    t.string "status"
    t.integer "owner_id", null: false
    t.string "slug"
    t.datetime "approved_at"
    t.integer "approved_by_id"
    t.string "confirmation_token"
    t.datetime "confirmation_sent_at"
    t.datetime "confirmed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approved_by_id"], name: "index_accounts_on_approved_by_id"
    t.index ["confirmation_token"], name: "index_accounts_on_confirmation_token", unique: true
    t.index ["owner_id"], name: "index_accounts_on_owner_id"
  end

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "case_studies", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "title"
    t.text "description"
    t.integer "position"
    t.boolean "published", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_case_studies_on_product_id"
  end

  create_table "categories", force: :cascade do |t|
    t.string "name"
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "company_profiles", force: :cascade do |t|
    t.integer "account_id", null: false
    t.string "company_name"
    t.string "slug"
    t.string "website"
    t.string "company_size"
    t.string "headquarters_location"
    t.string "contact_email"
    t.string "contact_phone"
    t.string "linkedin_url"
    t.text "description"
    t.boolean "published", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_company_profiles_on_account_id"
  end

  create_table "friendly_id_slugs", force: :cascade do |t|
    t.string "slug", null: false
    t.integer "sluggable_id", null: false
    t.string "sluggable_type", limit: 50
    t.string "scope"
    t.datetime "created_at"
    t.index ["slug", "sluggable_type", "scope"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type_and_scope", unique: true
    t.index ["slug", "sluggable_type"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type"
    t.index ["sluggable_type", "sluggable_id"], name: "index_friendly_id_slugs_on_sluggable_type_and_sluggable_id"
  end

  create_table "leads", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "account_id", null: false
    t.integer "user_id", null: false
    t.integer "agency_account_id"
    t.string "status"
    t.string "contact_name"
    t.string "contact_email"
    t.string "contact_phone"
    t.string "contact_company"
    t.string "timeline"
    t.string "budget_range"
    t.text "message"
    t.text "notes"
    t.string "source"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_leads_on_account_id"
    t.index ["agency_account_id"], name: "index_leads_on_agency_account_id"
    t.index ["product_id"], name: "index_leads_on_product_id"
    t.index ["user_id"], name: "index_leads_on_user_id"
  end

  create_table "product_content", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "title"
    t.text "description"
    t.integer "position"
    t.boolean "published", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_product_content_on_product_id"
  end

  create_table "product_videos", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "title"
    t.text "description"
    t.integer "position"
    t.boolean "published", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_product_videos_on_product_id"
  end

  create_table "products", force: :cascade do |t|
    t.integer "account_id", null: false
    t.integer "category_id"
    t.string "name"
    t.text "description"
    t.string "slug"
    t.string "pricing_model"
    t.boolean "published", default: false
    t.json "features", default: []
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_products_on_account_id"
    t.index ["category_id"], name: "index_products_on_category_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "team_invitations", force: :cascade do |t|
    t.integer "account_id", null: false
    t.integer "invited_by_id", null: false
    t.string "email"
    t.string "role"
    t.string "status", default: "pending"
    t.text "message"
    t.string "token"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "email"], name: "index_team_invitations_on_account_id_and_email", unique: true
    t.index ["account_id"], name: "index_team_invitations_on_account_id"
    t.index ["invited_by_id"], name: "index_team_invitations_on_invited_by_id"
    t.index ["token"], name: "index_team_invitations_on_token", unique: true
  end

  create_table "users", force: :cascade do |t|
    t.string "email_address", null: false
    t.string "password_digest", null: false
    t.string "first_name"
    t.string "last_name"
    t.string "phone"
    t.string "job_title"
    t.boolean "super_admin", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email_address"], name: "index_users_on_email_address", unique: true
    t.index ["super_admin"], name: "index_users_on_super_admin"
  end

  add_foreign_key "account_users", "accounts"
  add_foreign_key "account_users", "users"
  add_foreign_key "accounts", "users", column: "approved_by_id"
  add_foreign_key "accounts", "users", column: "owner_id"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "case_studies", "products"
  add_foreign_key "company_profiles", "accounts"
  add_foreign_key "leads", "accounts"
  add_foreign_key "leads", "accounts", column: "agency_account_id"
  add_foreign_key "leads", "products"
  add_foreign_key "leads", "users"
  add_foreign_key "product_content", "products"
  add_foreign_key "product_videos", "products"
  add_foreign_key "products", "accounts"
  add_foreign_key "products", "categories"
  add_foreign_key "sessions", "users"
  add_foreign_key "team_invitations", "accounts"
  add_foreign_key "team_invitations", "users", column: "invited_by_id"
end
