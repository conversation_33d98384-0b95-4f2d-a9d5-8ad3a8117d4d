class CreateInitialDatabase < ActiveRecord::Migration[8.0]
  def change
    # Create users table
    create_table :users do |t|
      t.string :email_address, null: false
      t.string :password_digest, null: false
      t.string :first_name
      t.string :last_name
      t.string :phone
      t.string :job_title
      t.boolean :super_admin, default: false, null: false

      t.timestamps
    end
    add_index :users, :email_address, unique: true
    add_index :users, :super_admin

    # Create sessions table
    create_table :sessions do |t|
      t.references :user, null: false, foreign_key: true
      t.string :ip_address
      t.string :user_agent

      t.timestamps
    end

    # Create accounts table
    create_table :accounts do |t|
      t.string :name
      t.string :account_type
      t.string :status
      t.references :owner, null: false, foreign_key: { to_table: :users }
      t.string :slug
      t.datetime :approved_at
      t.references :approved_by, foreign_key: { to_table: :users }
      t.string :confirmation_token
      t.datetime :confirmation_sent_at
      t.datetime :confirmed_at

      t.timestamps
    end
    add_index :accounts, :confirmation_token, unique: true

    # Create account_users join table
    create_table :account_users do |t|
      t.references :account, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.string :role
      t.datetime :joined_at

      t.timestamps
    end

    # Create company_profiles table
    create_table :company_profiles do |t|
      t.references :account, null: false, foreign_key: true
      t.string :company_name
      t.string :slug
      t.string :website
      t.string :company_size
      t.string :headquarters_location
      t.string :contact_email
      t.string :contact_phone
      t.string :linkedin_url
      t.text :description
      t.boolean :published, default: false

      t.timestamps
    end

    # Create categories table
    create_table :categories do |t|
      t.string :name
      t.string :slug

      t.timestamps
    end

    # Create products table
    create_table :products do |t|
      t.references :account, null: false, foreign_key: true
      t.references :category, foreign_key: true
      t.string :name
      t.text :description
      t.string :slug
      t.string :pricing_model
      t.boolean :published, default: false
      t.json :features, default: []

      t.timestamps
    end

    # Create product_videos table
    create_table :product_videos do |t|
      t.references :product, null: false, foreign_key: true
      t.string :title
      t.text :description
      t.integer :position
      t.boolean :published, default: false

      t.timestamps
    end

    # Create product_content table
    create_table :product_content do |t|
      t.references :product, null: false, foreign_key: true
      t.string :title
      t.text :description
      t.integer :position
      t.boolean :published, default: false

      t.timestamps
    end

    # Create case_studies table
    create_table :case_studies do |t|
      t.references :product, null: false, foreign_key: true
      t.string :title
      t.text :description
      t.integer :position
      t.boolean :published, default: false

      t.timestamps
    end

    # Create leads table
    create_table :leads do |t|
      t.references :product, null: false, foreign_key: true
      t.references :account, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :agency_account, foreign_key: { to_table: :accounts }
      t.string :status
      t.string :contact_name
      t.string :contact_email
      t.string :contact_phone
      t.string :contact_company
      t.string :timeline
      t.string :budget_range
      t.text :message
      t.text :notes
      t.string :source

      t.timestamps
    end

    # Create team_invitations table
    create_table :team_invitations do |t|
      t.references :account, null: false, foreign_key: true
      t.references :invited_by, null: false, foreign_key: { to_table: :users }
      t.string :email
      t.string :role
      t.string :status, default: "pending"
      t.text :message
      t.string :token
      t.datetime :expires_at

      t.timestamps
    end
    add_index :team_invitations, :token, unique: true
    add_index :team_invitations, [:account_id, :email], unique: true

    # Create FriendlyId slugs table
    create_table :friendly_id_slugs do |t|
      t.string   :slug,           null: false
      t.integer  :sluggable_id,   null: false
      t.string   :sluggable_type, limit: 50
      t.string   :scope
      t.datetime :created_at
    end
    add_index :friendly_id_slugs, [:slug, :sluggable_type], length: { slug: 140, sluggable_type: 50 }
    add_index :friendly_id_slugs, [:slug, :sluggable_type, :scope], length: { slug: 70, sluggable_type: 50, scope: 70 }, unique: true
    add_index :friendly_id_slugs, [:sluggable_type, :sluggable_id]
  end
end
