# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

# Create Categories
categories = [
  "Document Management",
  "Financial Management", 
  "Human Resources",
  "Project Management",
  "Communication Tools",
  "Data Analytics",
  "Security & Compliance",
  "Infrastructure & Cloud",
  "Public Services",
  "Emergency Management",
  "Digital Government",
  "Case Management"
]

categories.each do |category_name|
  Category.find_or_create_by!(name: category_name)
end

# Features are now stored as JSON on products, no longer need Feature model

puts "Created #{Category.count} categories"

# Create sample users and accounts for development
if Rails.env.development?
  # Create admin user (super admin)
  admin = User.find_or_create_by!(email_address: "<EMAIL>") do |user|
    user.password = "password123"
    user.first_name = "Admin"
    user.last_name = "User"
    user.super_admin = true
  end
  
  # Ensure admin has super admin privileges if already exists
  admin.update!(super_admin: true) unless admin.super_admin?

  # Create admin account
  admin_account = Account.find_or_create_by!(name: "Platia Admin") do |account|
    account.account_type = "admin"
    account.status = "approved"
    account.approved_at = 1.week.ago
    account.approved_by = admin
    account.owner = admin
    # Set account as confirmed
    account.confirmed_at = 1.week.ago
    account.confirmation_token = SecureRandom.urlsafe_base64(32)
    account.confirmation_sent_at = 2.weeks.ago
  end

  # Create AccountUser relationship for admin
  AccountUser.find_or_create_by!(account: admin_account, user: admin) do |au|
    au.role = "admin"
    au.joined_at = 1.week.ago
  end

  # Create vendor user and account
  vendor_user = User.find_or_create_by!(email_address: "<EMAIL>") do |user|
    user.password = "password123"
    user.first_name = "John"
    user.last_name = "Vendor"
  end

  vendor_account = Account.find_or_create_by!(name: "TechCorp Solutions") do |account|
    account.account_type = "vendor"
    account.status = "approved"
    account.approved_at = 1.week.ago
    account.approved_by = admin
    account.owner = vendor_user
    # Set account as confirmed
    account.confirmed_at = 1.week.ago
    account.confirmation_token = SecureRandom.urlsafe_base64(32)
    account.confirmation_sent_at = 2.weeks.ago
  end

  # Create AccountUser relationship
  AccountUser.find_or_create_by!(account: vendor_account, user: vendor_user) do |au|
    au.role = "admin"
    au.joined_at = 1.week.ago
  end

  # Create company profile
  company_profile = CompanyProfile.find_or_create_by!(account: vendor_account) do |profile|
    profile.company_name = "TechCorp Solutions"
    profile.website = "https://techcorp.example.com"
    profile.company_size = "51-200"
    profile.headquarters_location = "San Francisco, CA"
    profile.contact_email = "<EMAIL>"
    profile.contact_phone = "******-0123"
    profile.published = true
  end

  # Create agency user and account
  agency_user = User.find_or_create_by!(email_address: "<EMAIL>") do |user|
    user.password = "password123"
    user.first_name = "Jane"
    user.last_name = "Official"
  end

  agency_account = Account.find_or_create_by!(name: "City of Springfield") do |account|
    account.account_type = "agency"
    account.status = "approved"
    account.approved_at = 1.week.ago
    account.approved_by = admin
    account.owner = agency_user
    # Set account as confirmed
    account.confirmed_at = 1.week.ago
    account.confirmation_token = SecureRandom.urlsafe_base64(32)
    account.confirmation_sent_at = 2.weeks.ago
  end

  AccountUser.find_or_create_by!(account: agency_account, user: agency_user) do |au|
    au.role = "admin"
    au.joined_at = 1.week.ago
  end

  # Create sample products
  doc_mgmt = Category.find_by(name: "Document Management")
  public_services = Category.find_by(name: "Public Services")
  
  # Features are now stored as JSON on products

  product1 = Product.find_or_create_by!(name: "GovDoc Manager", account: vendor_account) do |product|
    product.pricing_model = "monthly"
    product.published = true
    product.category = doc_mgmt
    product.description = "GovDoc Manager provides comprehensive document management capabilities for government agencies. Features include document digitization and storage, advanced search and indexing, compliance tracking, automated workflows, and multi-level security."
  end

  product2 = Product.find_or_create_by!(name: "CitizenConnect Portal", account: vendor_account) do |product|
    product.pricing_model = "annual"
    product.published = false
    product.category = public_services
    product.description = "CitizenConnect Portal offers online service capabilities for citizens. Features include online service requests, status tracking, mobile responsive design, multi-language support, and integration APIs."
  end

  # Assign features as JSON
  if product1
    product1.update!(features: [
      { "name" => "Cloud-based" },
      { "name" => "Mobile App" },
      { "name" => "API Integration" }
    ])
  end

  if product2
    product2.update!(features: [
      { "name" => "Cloud-based" },
      { "name" => "Mobile App" },
      { "name" => "API Integration" }
    ])
  end

  # Create a sample team invitation
  TeamInvitation.find_or_create_by!(email: "<EMAIL>", account: vendor_account) do |invitation|
    invitation.invited_by = vendor_user
    invitation.role = "member"
    invitation.message = "Welcome to our team! We'd love to have you join us in building great government technology solutions."
  end

  # Create additional government tech companies
  gov_tech_companies = [
    {
      company_name: "CivicTech Solutions",
      website: "https://civictech.example.com",
      company_size: "11-50",
      headquarters_location: "Austin, TX",
      email: "<EMAIL>",
      products: [
        {
          name: "OpenGov Platform",
          pricing_model: "monthly",
          description: "Transparency portal with budget visualization, meeting management, and public comment system",
          category: "Digital Government",
          features: ["Cloud-based", "Dashboard", "Reporting"]
        },
        {
          name: "CitizenVoice",
          pricing_model: "annual", 
          description: "Survey management with community feedback, analytics dashboard, and mobile app",
          category: "Public Services",
          features: ["Mobile-friendly", "Real-time", "Dashboard"]
        }
      ]
    },
    {
      company_name: "GovDataFlow",
      website: "https://govdataflow.example.com",
      company_size: "201-500",
      headquarters_location: "Washington, DC",
      email: "<EMAIL>",
      products: [
        {
          name: "DataHub Enterprise",
          pricing_model: "annual",
          description: "Data integration with ETL pipelines, real-time analytics, and custom dashboards",
          category: "Data Analytics",
          features: ["Enterprise", "Real-time", "API Integration"]
        }
      ]
    },
    {
      company_name: "SecureGov Technologies",
      website: "https://securegov.example.com",
      company_size: "51-200",
      headquarters_location: "McLean, VA",
      email: "<EMAIL>",
      products: [
        {
          name: "CyberShield Pro",
          pricing_model: "monthly",
          description: "Advanced threat detection with compliance monitoring, incident response, and security analytics",
          category: "Security & Compliance",
          features: ["Enterprise", "Real-time", "AI/ML"]
        },
        {
          name: "ComplianceTracker",
          pricing_model: "annual",
          description: "Regulation tracking with audit management, risk assessment, and reporting tools",
          category: "Security & Compliance",
          features: ["Dashboard", "Reporting", "Workflow"]
        }
      ]
    },
    {
      company_name: "UrbanPlan Analytics",
      website: "https://urbanplan.example.com",
      company_size: "11-50",
      headquarters_location: "Portland, OR",
      email: "<EMAIL>",
      products: [
        {
          name: "CityMapper Pro",
          pricing_model: "monthly",
          description: "GIS mapping with zoning analysis, development tracking, and public consultation tools",
          category: "Digital Government",
          features: ["GIS", "Dashboard", "Cloud-based"]
        }
      ]
    },
    {
      company_name: "PublicWorks Digital",
      website: "https://publicworksdigital.example.com",
      company_size: "1-10",
      headquarters_location: "Denver, CO",
      email: "<EMAIL>",
      products: [
        {
          name: "AssetTrack 360",
          pricing_model: "monthly",
          description: "Asset inventory with maintenance scheduling, work order management, and mobile field app",
          category: "Infrastructure & Cloud",
          features: ["Mobile-friendly", "Workflow", "Real-time"]
        },
        {
          name: "InfraMonitor",
          pricing_model: "annual",
          description: "Infrastructure monitoring with predictive maintenance, IoT integration, and alert system",
          category: "Infrastructure & Cloud",
          features: ["Real-time", "AI/ML", "API Integration"]
        }
      ]
    },
    {
      company_name: "EmergencyResponse Systems",
      website: "https://emergencyresponse.example.com",
      company_size: "101-200",
      headquarters_location: "Phoenix, AZ",
      email: "<EMAIL>",
      products: [
        {
          name: "CrisisCommand",
          pricing_model: "annual",
          description: "Incident management with resource coordination, emergency alerts, and real-time communication",
          category: "Emergency Management",
          features: ["Real-time", "Mobile-friendly", "Enterprise"]
        }
      ]
    },
    {
      company_name: "TaxTech Innovations",
      website: "https://taxtech.example.com",
      company_size: "51-200",
      headquarters_location: "Atlanta, GA",
      email: "<EMAIL>",
      products: [
        {
          name: "RevenuePro",
          pricing_model: "annual",
          description: "Tax collection with payment processing, compliance reporting, and taxpayer portal",
          category: "Financial Management",
          features: ["Enterprise", "Dashboard", "API Integration"]
        },
        {
          name: "PropertyTax Manager",
          pricing_model: "monthly",
          description: "Property assessment with tax calculation, appeal management, and GIS integration",
          category: "Financial Management",
          features: ["GIS", "Workflow", "Dashboard"]
        }
      ]
    },
    {
      company_name: "HealthGov Solutions",
      website: "https://healthgov.example.com",
      company_size: "51-200",
      headquarters_location: "Baltimore, MD",
      email: "<EMAIL>",
      products: [
        {
          name: "HealthTracker Pro",
          pricing_model: "annual",
          description: "Disease surveillance with contact tracing, health reporting, and data analytics",
          category: "Public Services",
          features: ["HIPAA Compliant", "Real-time", "Dashboard"]
        }
      ]
    },
    {
      company_name: "EduGov Technologies",
      website: "https://edugov.example.com",
      company_size: "101-200",
      headquarters_location: "Sacramento, CA",
      email: "<EMAIL>",
      products: [
        {
          name: "SchoolAdmin Plus",
          pricing_model: "annual",
          description: "Student records with enrollment management, grade tracking, and parent portal",
          category: "Public Services",
          features: ["SaaS", "Mobile-friendly", "Dashboard"]
        },
        {
          name: "DistrictAnalytics",
          pricing_model: "monthly",
          description: "Performance analytics with budget tracking, resource planning, and compliance reporting",
          category: "Data Analytics",
          features: ["Dashboard", "Reporting", "AI/ML"]
        }
      ]
    },
    {
      company_name: "LegalTech Government",
      website: "https://legaltech.example.com",
      company_size: "11-50",
      headquarters_location: "Boston, MA",
      email: "<EMAIL>",
      products: [
        {
          name: "CourtCase Pro",
          pricing_model: "monthly",
          description: "Case management with document filing, scheduling system, and legal research tools",
          category: "Case Management",
          features: ["Workflow", "Cloud-based"]
        }
      ]
    },
    {
      company_name: "VoteTech Systems",
      website: "https://votetech.example.com",
      company_size: "51-200",
      headquarters_location: "Richmond, VA",
      email: "<EMAIL>",
      products: [
        {
          name: "ElectionManager",
          pricing_model: "annual",
          description: "Voter registration with ballot management, election reporting, and security features",
          category: "Digital Government",
          features: ["Enterprise", "Reporting"]
        }
      ]
    },
    {
      company_name: "PermitFlow Digital",
      website: "https://permitflow.example.com",
      company_size: "11-50",
      headquarters_location: "Seattle, WA",
      email: "<EMAIL>",
      products: [
        {
          name: "PermitPro",
          pricing_model: "monthly",
          description: "Online permit applications with review workflows, inspection scheduling, and payment processing",
          category: "Digital Government",
          features: ["Workflow", "Mobile-friendly", "API Integration"]
        },
        {
          name: "CodeEnforce",
          pricing_model: "annual",
          description: "Violation tracking with case management, photo documentation, and compliance monitoring",
          category: "Case Management",
          features: ["Mobile-friendly", "Workflow", "Real-time"]
        }
      ]
    },
    {
      company_name: "BudgetWise Government",
      website: "https://budgetwise.example.com",
      company_size: "51-200",
      headquarters_location: "Chicago, IL",
      email: "<EMAIL>",
      products: [
        {
          name: "BudgetMaster Pro",
          pricing_model: "annual",
          description: "Budget planning with expense tracking, financial reporting, and forecasting tools",
          category: "Financial Management",
          features: ["Enterprise", "Dashboard", "Reporting"]
        }
      ]
    },
    {
      company_name: "WorkforceGov Solutions",
      website: "https://workforcegov.example.com",
      company_size: "101-200",
      headquarters_location: "Nashville, TN",
      email: "<EMAIL>",
      products: [
        {
          name: "GovHR Pro",
          pricing_model: "monthly",
          description: "Employee records with payroll processing, benefits management, and performance tracking",
          category: "Human Resources",
          features: ["SaaS", "Dashboard", "API Integration"]
        },
        {
          name: "TrainingTracker",
          pricing_model: "annual",
          description: "Training management with certification tracking, compliance monitoring, and progress reporting",
          category: "Human Resources",
          features: ["Workflow", "Reporting", "Dashboard"]
        }
      ]
    }
  ]

  # Create companies and their products
  gov_tech_companies.each_with_index do |company_data, index|
    # Create user for this company
    company_user = User.find_or_create_by!(email_address: company_data[:email]) do |user|
      user.password = "password123"
      user.first_name = "CEO"
      user.last_name = company_data[:company_name].split.first
    end

    # Create vendor account
    vendor_account = Account.find_or_create_by!(name: company_data[:company_name]) do |account|
      account.account_type = "vendor"
      account.status = "approved"
      account.approved_at = rand(1..30).days.ago
      account.approved_by = admin
      account.owner = company_user
      # Set account as confirmed
      account.confirmed_at = rand(1..30).days.ago
      account.confirmation_token = SecureRandom.urlsafe_base64(32)
      account.confirmation_sent_at = rand(31..60).days.ago
    end

    # Create AccountUser relationship
    AccountUser.find_or_create_by!(account: vendor_account, user: company_user) do |au|
      au.role = "admin"
      au.joined_at = rand(1..30).days.ago
    end

    # Create company profile
    company_profile = CompanyProfile.find_or_create_by!(account: vendor_account) do |profile|
      profile.company_name = company_data[:company_name]
      profile.website = company_data[:website]
      profile.company_size = company_data[:company_size]
      profile.headquarters_location = company_data[:headquarters_location]
      profile.contact_email = company_data[:email]
      profile.contact_phone = "******-#{sprintf('%04d', rand(1000..9999))}"
      profile.published = true
      profile.description = "#{company_data[:company_name]} specializes in solutions for government agencies. We provide innovative technology solutions that help modernize public sector operations and improve citizen services."
    end

    # Create products for this company
    company_data[:products].each do |product_data|
      # Find the category
      category = Category.find_by(name: product_data[:category])
      
      product = Product.find_or_create_by!(name: product_data[:name], account: vendor_account) do |p|
        p.pricing_model = product_data[:pricing_model]
        p.published = true
        p.category = category
        p.description = product_data[:description]
      end

      # Assign features as JSON
      features_json = product_data[:features].map { |name| { "name" => name } }
      product.update!(features: features_json)
    end

    print "." # Progress indicator
  end

  puts "\n\nCreated development seed data:"
  puts "- Admin user: <EMAIL>"
  puts "- Vendor user: <EMAIL>" 
  puts "- Agency user: <EMAIL>"
  puts "- Password for all: password123"
  puts "- Created #{Account.where(account_type: 'vendor').count} vendor companies"
  puts "- Created #{Product.count} total products"
  puts "- Created #{TeamInvitation.count} team invitations"
end