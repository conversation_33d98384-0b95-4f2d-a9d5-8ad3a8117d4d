# Platia - Feature Scope & Implementation Plan (Simplified)

## 0. Prework
- Already created new Rails project
- Setup actiontext, activestorage, rails 8 standard auth
- Installed tailwindCSS

## 1. Feature Scope Overview

### Core User Types
1. **Vendors** - Software companies showcasing products
2. **Government Officials** - Browse and connect with vendors
3. **Admin** - Platform administrators (developers/founders)

### Key Features by User Type

#### Vendor Features
- Company profile management (published/unpublished status)
- Product management (multiple products with rich content)
- Team management (admin/member roles)
- Personal user profile

#### Government Official Features
- Marketplace browsing with search/filters
- Product discovery by categories and tags
- Profile management

#### Admin Features
- User management with impersonation
- Manual account approval/disapproval
- Platform statistics
- Email template previews

## 2. Database Schema

### Core Models

```ruby
# Users & Authentication
User
- email
- password_digest
- first_name
- last_name
- phone
- job_title
- confirmed_at
- approved_at
- approved_by_id (references User)

# Multi-tenancy
Account
- name
- account_type (enum: vendor, government)
- status (enum: pending, approved, rejected)
- owner_id (references User)

AccountUser
- account_id
- user_id
- role (enum: admin, member)
- joined_at

# Company Information (Vendor-specific)
CompanyProfile
- account_id
- company_name
- slug (for URL via FriendlyId)
- description
- website
- company_size
- headquarters_location
- service_areas (array/JSON)
- certifications (array/JSON)
- logo_url
- cover_image_url
- published (boolean)
- contact_email
- contact_phone
- linkedin_url
- twitter_url

# Products & Content
Product
- account_id
- name
- slug (via FriendlyId)
- description
- key_features (JSON/array)
- pricing_model
- deployment_options (array)
- published (boolean)

Category
- name
- slug (via FriendlyId)

ProductCategory
- product_id
- category_id

Tag
- name
- slug (via FriendlyId)

ProductTag
- product_id
- tag_id

# Product Videos
ProductVideo
- product_id
- title
- description
- video_url (or use Active Storage)
- video_type (enum: demo, tutorial, other)
- position
- published (boolean)

# Product Media (misc attachments)
ProductMedia
- product_id
- title
- description
- file_url (or use Active Storage)
- media_type (enum: pdf, document, presentation, video, other)
- position

# Case Studies
CaseStudy
- product_id
- title
- description
- client_name
- implementation_date
- results_summary
- attachment_url (or use Active Storage)
- attachment_type (enum: pdf, video, presentation)
- position
- published (boolean)

# Lead Management
Lead
- product_id
- account_id (government account)
- user_id (government user)
- status (enum: new, contacted, qualified, closed)
- notes
- created_at
```

## 3. Implementation Plan

### Phase 1: Foundation (Week 1)
1. **Setup Rails 8 project**
   - Initialize with Rails 8 authentication generator
   - Configure Tailwind CSS
   - Setup StimulusJS
   - Configure SQLite database
   - Add gems: friendly_id, chartkick

2. **Core Models & Authentication**
   - Implement User, Account, AccountUser models
   - Setup multi-tenancy logic
   - Create authentication flows with email confirmation
   - Implement team invitation system

3. **Base Controllers & Views**
   ```ruby
   # Key controllers structure
   - ApplicationController (with current_account helper)
   - Dashboard::BaseController (vendor dashboard parent)
   - Government::BaseController (gov dashboard parent)
   - Admin::BaseController (admin dashboard parent)
   ```

### Phase 2: Vendor Features (Week 2-3)
1. **Company Profile**
   - CompanyProfile CRUD with published/unpublished status
   - Rich text editing for descriptions
   - Logo/cover image uploads with Active Storage
   - Social media links
   - Stimulus controllers:
     - Image upload with preview
     - Form autosave
     - Publish toggle

2. **Product Management**
   - Product CRUD operations
   - Demo video uploads (support for multiple videos)
   - Case study management (title, description, attachment)
   - Additional media uploads (PDFs, documents, videos)
   - Category and tag management
   - Stimulus controllers:
     - Dynamic form fields for features
     - Drag-and-drop for reordering videos/media/case studies
     - File type validation
     - Preview toggles

3. **Team Management**
   - Invite team members
   - Role management (admin/member)
   - Remove team members

### Phase 3: Government Features (Week 4)
1. **Marketplace**
   - Company listing with pagination
   - Product listing with filters
   - Search implementation
   - Filter system with Stimulus
   - Category/tag filtering

2. **Product Interest Flow**
   - Express interest in products
   - Automatic lead creation
   - Email notifications to vendors

3. **Profile Management**
   - Edit user information
   - Organization details in Account model

### Phase 4: Admin Dashboard (Week 5)
1. **User Management**
   - List all users with search
   - Approve/reject vendor and government accounts
   - User impersonation
   - Edit user details

2. **Platform Statistics**
   - Total vendors/government users
   - Products by category
   - Recent signups
   - Pending approvals
   - Charts using Chartkick for visual representation

3. **Email Template Previews**
   - List of all system emails
   - Preview functionality for ActionMailer templates

### Phase 5: Polish & Launch Prep (Week 6)
1. **Email System**
   - Welcome emails (vendor & government)
   - Account approval/rejection emails
   - Interest notification emails
   - Company page published notification

2. **SEO & Performance**
   - SEO-friendly URLs using slugs
   - Meta tags for company/product pages
   - Image optimization
   - Caching strategy

3. **Security & Testing**
   - Authorization policies
   - Security audit
   - Core feature tests

## 4. Key Stimulus Controllers

```javascript
// app/javascript/controllers/

1. image_upload_controller.js
   - Drag and drop functionality
   - Preview generation
   - File size validation

2. search_filter_controller.js
   - Live search
   - Filter toggles
   - Results updating without page reload

3. form_validation_controller.js
   - Real-time validation
   - Error display
   - Submit button state

4. autosave_controller.js
   - Periodic form saving
   - Draft status indicator
   - Conflict resolution

5. publish_toggle_controller.js
   - Published/unpublished state
   - Confirmation dialogs
   - Visual status indicators

6. dynamic_fields_controller.js
   - Add/remove form fields
   - Reordering capability
   - Nested attributes handling

7. media_manager_controller.js
   - Separate sections for videos, case studies, media
   - Drag-and-drop reordering within sections
   - File type validation per section
   - Position updating via AJAX

8. video_preview_controller.js
   - Video thumbnail generation
   - Play/pause preview
   - Duration display
```

## 5. Controller Structure Example

```ruby
# app/controllers/dashboard/products_controller.rb
module Dashboard
  class ProductsController < BaseController
    before_action :require_vendor_account
    before_action :set_product, only: [:show, :edit, :update, :destroy]

    def index
      @products = current_account.products.includes(:categories)
    end

    def new
      @product = current_account.products.build
    end

    def create
      @product = current_account.products.build(product_params)

      if @product.save
        redirect_to dashboard_products_path,
                    notice: 'Product created successfully'
      else
        render :new
      end
    end

    private

    def set_product
      @product = current_account.products.friendly.find(params[:id])
    end

    def product_params
      params.require(:product).permit(
        :name, :description, :pricing_model, :published,
        key_features: [], deployment_options: [],
        category_ids: [], tag_ids: []
      )
    end
  end
end

# app/controllers/dashboard/product_videos_controller.rb
module Dashboard
  class ProductVideosController < BaseController
    before_action :set_product
    before_action :set_video, only: [:update, :destroy]

    def create
      @video = @product.product_videos.build(video_params)

      if @video.save
        render json: { status: 'success', id: @video.id }
      else
        render json: { status: 'error', errors: @video.errors }
      end
    end

    def update_positions
      params[:positions].each do |id, position|
        @product.product_videos.find(id).update(position: position)
      end

      head :ok
    end

    private

    def set_product
      @product = current_account.products.friendly.find(params[:product_id])
    end

    def set_video
      @video = @product.product_videos.find(params[:id])
    end

    def video_params
      params.require(:product_video).permit(
        :title, :description, :video_type, :video_file, :published
      )
    end
  end
end
```

## 6. Technical Considerations

### Account & Profile Relationship
```ruby
# app/models/account.rb
class Account < ApplicationRecord
  extend FriendlyId
  friendly_id :name, use: :slugged

  has_one :company_profile, dependent: :destroy
  has_many :products, dependent: :destroy

  def vendor?
    account_type == 'vendor'
  end

  def government?
    account_type == 'government'
  end
end

# app/models/company_profile.rb
class CompanyProfile < ApplicationRecord
  extend FriendlyId
  friendly_id :company_name, use: :slugged

  belongs_to :account
  has_one_attached :logo
  has_one_attached :cover_image

  validates :company_name, presence: true

  scope :published, -> { where(published: true) }
end

# app/models/product.rb
class Product < ApplicationRecord
  extend FriendlyId
  friendly_id :name, use: :slugged

  belongs_to :account
  has_many :product_videos, -> { order(:position) }, dependent: :destroy
  has_many :product_media, -> { order(:position) }, dependent: :destroy
  has_many :case_studies, -> { order(:position) }, dependent: :destroy
  has_many :product_categories, dependent: :destroy
  has_many :categories, through: :product_categories
  has_many :product_tags, dependent: :destroy
  has_many :tags, through: :product_tags

  scope :published, -> { where(published: true) }

  def primary_demo_video
    product_videos.where(video_type: 'demo').first
  end
end

# app/models/product_video.rb
class ProductVideo < ApplicationRecord
  belongs_to :product
  has_one_attached :video_file # If storing files

  validates :title, presence: true
  validates :video_type, inclusion: { in: %w[demo tutorial other] }

  scope :demos, -> { where(video_type: 'demo') }
  scope :published, -> { where(published: true) }
end

# app/models/case_study.rb
class CaseStudy < ApplicationRecord
  belongs_to :product
  has_one_attached :attachment

  validates :title, :description, :client_name, presence: true
  validates :attachment_type, inclusion: { in: %w[pdf video presentation] }

  scope :published, -> { where(published: true) }
end
```

### Security & Authorization
- Ensure all queries are scoped to current_account
- Validate file uploads (size, type)
- Sanitize all user input

### Performance
- Eager load associations in marketplace views
- Use Active Storage variants for images
- Implement Russian doll caching for product listings
- Consider pagination for large datasets

## 7. Simplified Timeline
- **Week 1**: Foundation & Authentication
- **Week 2-3**: Vendor Features (Profiles & Products)
- **Week 4**: Government Features & Marketplace
- **Week 5**: Admin Dashboard
- **Week 6**: Polish, Testing & Deployment

## 8. Key Routes Structure
```ruby
# config/routes.rb
Rails.application.routes.draw do
  # Authentication
  resources :sessions
  resources :registrations

  # Public marketplace
  resources :companies, only: [:index, :show] do
    resources :products, only: [:show]
  end

  # Vendor dashboard
  namespace :dashboard do
    resource :company_profile
    resources :products
    resources :team_members
    root to: 'overview#index'
  end

  # Government dashboard
  namespace :government do
    resources :interests
    resource :profile
    root to: 'marketplace#index'
  end

  # Admin
  namespace :admin do
    resources :users do
      member do
        post :approve
        post :reject
        post :impersonate
      end
    end
    resources :email_previews, only: [:index, :show]
    root to: 'overview#index'
  end

  root to: 'home#index'
end
```
