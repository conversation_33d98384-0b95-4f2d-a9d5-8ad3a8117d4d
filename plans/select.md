<div class="sm:col-span-3">
            <%= form.label :status, class: "block text-sm/6 font-medium text-gray-900 dark:text-white" %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :status,
                  options_for_select([
                    ['Draft', 'draft'],
                    ['Published', 'published']
                  ], document.new_record? ? 'draft' : document.status),
                  { prompt: document.new_record? ? nil : "Select status" },
                  { 
                    required: true,
                    class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm/6" 
                  } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
            <p class="mt-3 text-sm/6 text-gray-600 dark:text-gray-400">Select a status. Only published updates can be sent.</p>
          </div>