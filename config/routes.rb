Rails.application.routes.draw do
  mount Mailbin::Engine => :mailbin if Rails.env.development?
  
  get "registrations/new"
  get "registrations/create"
  get "pages/landing"
  get "pages/terms_of_service"
  get "pages/privacy_policy"
  get "pending", to: "pages#pending"
  # Authentication
  resource :session
  resources :passwords, param: :token

  # Public marketplace
  resources :companies, only: [:index, :show] do
    resources :products, only: [:show]
  end

  # Vendor dashboard
  namespace :vendors do
    resource :company_profile do
      member do
        patch :update_logo
        delete :remove_logo
      end
    end
    resources :products do
      resources :product_videos do
        member do
          patch :update_position
        end
      end
      resources :case_studies do
        member do
          patch :update_position
        end
      end
      resources :product_contents do
        member do
          patch :update_position
        end
      end
    end
    resources :leads, only: [:index, :show, :update]
    root to: 'overview#index'
  end

  # Agency dashboard
  namespace :agencies do
    get 'dashboard', to: 'dashboard#index'
    
    resources :products, only: [] do
      resources :interests, only: [:create]
    end
    
    resources :interests, only: [:index]
    root to: 'dashboard#index'
  end

  # Admin
  namespace :admin do
    resources :accounts do
      member do
        post :approve
        post :reject
      end
    end
    
    resources :companies
    resources :products do
      resources :product_videos do
        member do
          patch :update_position
        end
      end
      resources :case_studies do
        member do
          patch :update_position
        end
      end
      resources :product_contents do
        member do
          patch :update_position
        end
      end
    end
    resources :categories
    
    resources :email_previews, only: [:index, :show]
    resource :health, only: [:show]
    root to: 'overview#index'
  end

  # Mission Control Jobs (Super Admin Only)
  mount MissionControl::Jobs::Engine, at: "/admin/jobs"

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # SEO
  get "sitemap.xml" => "sitemaps#index", as: :sitemap, defaults: { format: 'xml' }
  get "robots.txt" => "sitemaps#robots", as: :robots, defaults: { format: 'txt' }

  # Error pages
  get "errors/not_found", to: "errors#not_found"
  get "errors/unprocessable_entity", to: "errors#unprocessable_entity"
  get "errors/internal_server_error", to: "errors#internal_server_error"
  
  # Standard Rails error handling paths
  get "/404", to: "errors#not_found"
  get "/422", to: "errors#unprocessable_entity"
  get "/500", to: "errors#internal_server_error"

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # User registration
  resources :registrations, only: [:new, :create]
  
  # Account confirmation
  get 'confirm/:token', to: 'account_confirmations#show', as: :confirm_account
  get 'resend_confirmation', to: 'account_confirmations#new', as: :new_account_confirmation
  post 'resend_confirmation', to: 'account_confirmations#create', as: :account_confirmations
  
  # Team invitation acceptance
  get 'invitations/:token', to: 'invitation_acceptances#show', as: :invitation
  get 'invitations/:token/accept', to: 'invitation_acceptances#show', as: :accept_invitation
  post 'invitations/:token/accept', to: 'invitation_acceptances#accept'
  get 'invitations/:token/decline', to: 'invitation_acceptances#show', as: :decline_invitation  
  post 'invitations/:token/decline', to: 'invitation_acceptances#decline'
  
  # User account (top-level)
  resource :account, only: [:show, :edit, :update] do
    member do
      patch :update_profile
      patch :update_password
      patch :update_profile_photo
      delete :remove_profile_photo
    end
    
    # Team management
    resources :team_members, controller: 'accounts/team_members', except: [:show] do
      member do
        patch :update_role
      end
    end
    resources :team_invitations, controller: 'accounts/team_invitations', only: [:index, :new, :create, :destroy]
  end
  
  # Public marketplace (accessible to all users)
  get 'marketplace', to: 'marketplace#index'
  get 'marketplace/companies', to: 'marketplace#companies', as: :marketplace_companies
  get 'marketplace/products', to: 'marketplace#products', as: :marketplace_products
  get 'marketplace/companies/:id', to: 'marketplace#company', as: :marketplace_company
  get 'marketplace/products/:id', to: 'marketplace#product', as: :marketplace_product
  get 'marketplace/products/:product_id/videos/:id', to: 'marketplace#product_video', as: :marketplace_product_video
  get 'marketplace/products/:product_id/case_studies/:id', to: 'marketplace#case_study', as: :marketplace_case_study
  get 'marketplace/products/:product_id/content/:id', to: 'marketplace#product_content', as: :marketplace_product_content

  # Root path
  root to: 'pages#landing'
end
