# Mission Control Jobs Configuration
# Only allow super admin access

MissionControl::Jobs.base_controller_class = "Admin::BaseController"

# Configure authentication
# Rails.application.config.to_prepare do
#   MissionControl::Jobs::ApplicationController.class_eval do
#     before_action :require_authentication
#     before_action :require_super_admin

#     private

#     def require_super_admin
#       unless current_user&.super_admin?
#         redirect_to root_path, alert: 'Access denied. Super admin privileges required.'
#       end
#     end
#   end
# end