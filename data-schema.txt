Data Schema Documentation

This document describes the data structure of the Platia marketplace in human-readable terms.

Users

A User represents an individual person who can access the system.

- Email Address: Short text input (required)
- Password: Password input (required, stored securely)
- First Name: Short text input
- Last Name: Short text input
- Phone: Short text input
- Job Title: Short text input
- Confirmed At: Date/time when the user confirmed their email
- Super Admin: Yes/No checkbox (for system administrators)

Accounts

An Account represents an organization (either a vendor company or government agency) in the marketplace.

- Name: Short text input (organization name)
- Account Type: Dropdown selection (vendor or agency)
- Status: Dropdown selection (current status of the account)
- Owner: This refers to a User (the person who owns this account)
- Slug: Short text input (URL-friendly identifier)
- Approved At: Date/time when the account was approved
- Approved By: This refers to a User (the admin who approved the account)
- Confirmation Token: System-generated token for email confirmation
- Confirmation Sent At: Date/time when confirmation email was sent
- Confirmed At: Date/time when account was confirmed

Relationships:
- An Account belongs to a User (owner)
- An Account can have many Users (through Account Users)
- An Account can have one Company Profile
- An Account can have many Products
- An Account can have many Leads

Account Users

This connects Users to Accounts (allowing multiple people to be part of an organization).

- Role: Short text input (e.g., "admin", "member")
- Joined At: Date/time when the user joined this account

Relationships:
- This refers to an Account
- This refers to a User

Company Profiles

A Company Profile contains detailed information about a vendor company.

- Company Name: Short text input
- Slug: Short text input (URL-friendly identifier)
- Website: Short text input (company website URL)
- Company Size: Dropdown selection (e.g., "1-10", "11-50", "51-200")
- Headquarters Location: Short text input
- Contact Email: Short text input
- Contact Phone: Short text input
- LinkedIn URL: Short text input
- Description: Long text input (company description)
- Published: Yes/No checkbox (whether the profile is public)

Relationships:
- This belongs to an Account
- Has attachment (logo)

Products

A Product represents a software or service offering from a vendor.

- Name: Short text input (product name)
- Description: Long text input (detailed product description)
- Slug: Short text input (URL-friendly identifier)
- Pricing Model: Short text input (e.g., "subscription", "one-time")
- Published: Yes/No checkbox (whether the product is publicly visible)

Relationships:
- This belongs to an Account (the vendor)
- This belongs to a Category
- This has many Case Studies
- This has many Product Videos
- This has many Product Content items (extra vids/pdfs/etc.)
- This has many Features (user generated)
- This has many Leads
- Has attachments (product images, documents, etc.)

Other items
- The first video would be the demo video
- Question - Would each product from a vendor have separate certifications? And would that be defined certs by us or by the vendor? 

Categories

A Category groups similar products together.

- Name: Short text input (category name)
- Slug: Short text input (URL-friendly identifier)

Relationships:
- This has many Products


Case Studies

A Case Study showcases how a product was successfully used by a customer.

- Title: Short text input (case study title)
- Description: Long text input (detailed case study content)
- Position: Number input (for ordering case studies)
- Published: Yes/No checkbox (whether the case study is public)

Relationships:
- This belongs to a Product
- Has attachments (images, documents, etc.)

Product Videos

A Product Video is a video demonstration or explanation of a product.

- Title: Short text input (video title)
- Description: Long text input (video description)
- Position: Number input (for ordering videos)
- Published: Yes/No checkbox (whether the video is public)

Relationships:
- This belongs to a Product
- Has attachments (video files)

Product Content

Product Content represents additional content sections for a product (like features, specifications, etc.).

- Title: Short text input (content section title)
- Description: Long text input (content details)
- Position: Number input (for ordering content sections)
- Published: Yes/No checkbox (whether the content is public)

Relationships:
- This belongs to a Product
- Has attachments (images, documents, etc.)

Leads

A Lead represents a potential customer inquiry about a product.

- Status: Dropdown selection (e.g., "new", "contacted", "qualified")
- Contact Name: Short text input (inquirer's name)
- Contact Email: Short text input (inquirer's email)
- Contact Phone: Short text input (inquirer's phone)
- Contact Company: Short text input (inquirer's organization)
- Timeline: Short text input (when they need the solution)
- Budget Range: Short text input (their budget expectations)
- Message: Long text input (their inquiry message)
- Notes: Long text input (internal notes about the lead)
- Source: Short text input (how they found the product)

Relationships:
- This refers to a Product (the product they're interested in)
- This refers to an Account (the vendor who owns the product)
- This refers to a User (the person who submitted the inquiry)
- This refers to an Agency Account (if submitted by a government agency)

Team Invitations

A Team Invitation is sent to invite someone to join an organization's account.

- Email: Short text input (email of person being invited)
- Role: Short text input (their role in the organization)
- Status: Dropdown selection (e.g., "pending", "accepted", "expired")
- Message: Long text input (personal message with invitation)
- Token: System-generated invitation token
- Expires At: Date/time when the invitation expires

Relationships:
- This refers to an Account (the organization they're being invited to)
- This refers to a User (the person who sent the invitation)