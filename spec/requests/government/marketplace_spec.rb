require 'rails_helper'

RSpec.describe "Government::Marketplace", type: :request do
  let(:government_user) { create(:user) }
  let(:government_account) { create(:government_account, user: government_user) }
  let!(:published_product) { create(:detailed_product, published: true) }
  let!(:featured_product) { create(:featured_product, published: true) }
  
  before do
    # Simulate authenticated government user
    allow_any_instance_of(ApplicationController).to receive(:current_user).and_return(government_user)
    allow_any_instance_of(ApplicationController).to receive(:current_account).and_return(government_account)
  end

  describe "GET /government/marketplace" do
    it "displays the marketplace home page" do
      get marketplace_path
      
      expect(response).to have_http_status(:success)
      expect(response.body).to include("Discover Government Technology Solutions")
      expect(response.body).to include("Browse by Category")
    end
    
    it "shows featured products" do
      get marketplace_path
      
      expect(response.body).to include(featured_product.name)
    end
  end

  describe "GET /government/marketplace/products" do
    context "without filters" do
      it "displays all published products" do
        get marketplace_products_path
        
        expect(response).to have_http_status(:success)
        expect(response.body).to include(published_product.name)
      end
    end
    
    context "with search query" do
      it "filters products by search term" do
        get marketplace_products_path, params: { search: published_product.name }
        
        expect(response).to have_http_status(:success)
        expect(response.body).to include(published_product.name)
      end
    end
    
    context "with category filter" do
      let(:category) { create(:category) }
      
      before do
        published_product.categories << category
      end
      
      it "filters products by category" do
        get marketplace_products_path, params: { category_id: category.id }
        
        expect(response).to have_http_status(:success)
        expect(response.body).to include(published_product.name)
      end
    end
  end

  describe "GET /government/products/:id" do
    context "with published product" do
      it "displays product details" do
        get marketplace_product_path(published_product)
        
        expect(response).to have_http_status(:success)
        expect(response.body).to include(published_product.name)
        expect(response.body).to include("Express Interest")
      end
    end
    
    context "with unpublished product" do
      let(:unpublished_product) { create(:product, published: false) }
      
      it "returns 404" do
        expect {
          get marketplace_product_path(unpublished_product)
        }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  describe "POST /government/products/:id/interests" do
    let(:interest_params) do
      {
        message: "We are interested in this solution for our department",
        contact_name: "John Doe",
        contact_email: "<EMAIL>",
        contact_phone: "555-1234"
      }
    end
    
    it "creates an interest record" do
      expect {
        post marketplace_product_interests_path(published_product), params: { interest: interest_params }
      }.to change(Interest, :count).by(1)
      
      expect(response).to redirect_to(marketplace_product_path(published_product))
      expect(flash[:notice]).to include("interest has been submitted")
    end
    
    it "sends notification email to vendor" do
      expect {
        post marketplace_product_interests_path(published_product), params: { interest: interest_params }
      }.to have_enqueued_mail(UserMailer, :new_interest_notification)
    end
    
    context "with invalid params" do
      let(:invalid_params) { { message: "" } }
      
      it "does not create interest" do
        expect {
          post marketplace_product_interests_path(published_product), params: { interest: invalid_params }
        }.not_to change(Interest, :count)
        
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "GET /government/interests" do
    let!(:interest) { create(:interest, government_account: government_account, product: published_product) }
    
    it "displays user's interests" do
      get government_interests_path
      
      expect(response).to have_http_status(:success)
      expect(response.body).to include(published_product.name)
      expect(response.body).to include(interest.message)
    end
  end
end