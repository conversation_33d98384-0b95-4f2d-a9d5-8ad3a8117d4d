require 'rails_helper'

RSpec.describe "Authentication", type: :request do
  let(:user) { create(:user, password_digest: BCrypt::Password.create('password123')) }
  
  describe "POST /session" do
    context "with valid credentials" do
      it "creates a session and redirects" do
        post session_path, params: {
          email_address: user.email_address,
          password: 'password123'
        }
        
        expect(response).to have_http_status(:redirect)
        expect(session[:current_session_id]).to be_present
      end
    end
    
    context "with invalid credentials" do
      it "renders sign in form with error" do
        post session_path, params: {
          email_address: user.email_address,
          password: 'wrong_password'
        }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.body).to include("Try another email address or password")
      end
    end
    
    context "with unverified user" do
      let(:unverified_user) { create(:unverified_user, password_digest: BCrypt::Password.create('password123')) }
      
      it "does not create session" do
        post session_path, params: {
          email_address: unverified_user.email_address,
          password: 'password123'
        }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(session[:current_session_id]).to be_nil
      end
    end
  end
  
  describe "DELETE /session" do
    before do
      # Simulate logged in user
      session = user.sessions.create!
      session[:current_session_id] = session.id
    end
    
    it "destroys session and redirects to sign in" do
      delete session_path
      
      expect(response).to redirect_to(new_session_path)
      expect(session[:current_session_id]).to be_nil
    end
  end
end