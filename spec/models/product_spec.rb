require 'rails_helper'

RSpec.describe Product, type: :model do
  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:account_id) }
    it { is_expected.to validate_presence_of(:features) }
  end

  describe 'associations' do
    it { is_expected.to belong_to(:account) }
    it { is_expected.to have_many(:product_videos) }
    it { is_expected.to have_many(:product_content) }
    it { is_expected.to have_many(:case_studies) }
    it { is_expected.to belong_to(:category).optional }
    it { is_expected.to have_many(:leads) }
    it { is_expected.to have_rich_text(:description) }
  end

  describe 'scopes' do
    let!(:published_product) { create(:product, published: true) }
    let!(:unpublished_product) { create(:product, published: false) }
    let!(:featured_product) { create(:product, featured: true) }

    describe '.published' do
      it 'returns only published products' do
        expect(Product.published).to include(published_product)
        expect(Product.published).not_to include(unpublished_product)
      end
    end

    describe '.featured' do
      it 'returns only featured products' do
        expect(Product.featured).to include(featured_product)
      end
    end

    describe '.recent' do
      it 'orders by created_at desc' do
        older_product = create(:product, created_at: 2.days.ago)
        newer_product = create(:product, created_at: 1.day.ago)
        
        expect(Product.recent.first).to eq(newer_product)
        expect(Product.recent.last).to eq(older_product)
      end
    end
  end

  describe '#published?' do
    context 'when product is published' do
      let(:product) { create(:product, published: true) }
      
      it 'returns true' do
        expect(product.published?).to be true
      end
    end

    context 'when product is not published' do
      let(:product) { create(:product, published: false) }
      
      it 'returns false' do
        expect(product.published?).to be false
      end
    end
  end


  describe '.search_optimized' do
    let!(:product1) { create(:product, name: 'Cloud Analytics Platform') }
    let!(:product2) { create(:product, name: 'Security Management Tool') }
    
    context 'with matching query' do
      it 'returns products matching the query' do
        results = Product.search_optimized('Analytics')
        expect(results).to include(product1)
        expect(results).not_to include(product2)
      end
    end

    context 'with blank query' do
      it 'returns empty relation' do
        expect(Product.search_optimized('')).to be_empty
        expect(Product.search_optimized(nil)).to be_empty
      end
    end
  end

  describe '.pricing_models' do
    it 'returns hash of pricing model options' do
      expected_models = {
        'monthly' => 'Monthly',
        'annual' => 'Annual',
        'one_time' => 'One-time',
        'custom' => 'Custom',
        'free' => 'Free'
      }

      expect(Product.pricing_models).to eq(expected_models)
    end
  end

  describe 'JSON features functionality' do
    let(:product) { create(:product) }

    describe '#feature_names' do
      it 'returns array of feature names' do
        product.features = [
          { "name" => "Cloud-based" },
          { "name" => "Mobile App" }
        ]
        expect(product.feature_names).to eq(["Cloud-based", "Mobile App"])
      end

      it 'returns empty array when no features' do
        product.features = []
        expect(product.feature_names).to eq([])
      end
    end

    describe '#add_feature' do
      it 'adds a new feature' do
        product.features = []
        product.add_feature("New Feature")
        expect(product.features).to eq([{ "name" => "New Feature" }])
      end

      it 'strips whitespace from feature name' do
        product.features = []
        product.add_feature("  Spaced Feature  ")
        expect(product.features).to eq([{ "name" => "Spaced Feature" }])
      end

      it 'does not add blank features' do
        product.features = []
        product.add_feature("")
        product.add_feature(nil)
        expect(product.features).to eq([])
      end
    end

    describe '#remove_feature' do
      it 'removes an existing feature' do
        product.features = [
          { "name" => "Keep This" },
          { "name" => "Remove This" }
        ]
        product.remove_feature("Remove This")
        expect(product.features).to eq([{ "name" => "Keep This" }])
      end

      it 'does nothing if feature does not exist' do
        original_features = [{ "name" => "Existing Feature" }]
        product.features = original_features
        product.remove_feature("Non-existent Feature")
        expect(product.features).to eq(original_features)
      end
    end

    describe '#has_feature?' do
      before do
        product.features = [
          { "name" => "Cloud-based" },
          { "name" => "Mobile App" }
        ]
      end

      it 'returns true for existing feature' do
        expect(product.has_feature?("Cloud-based")).to be true
      end

      it 'returns false for non-existing feature' do
        expect(product.has_feature?("Non-existent")).to be false
      end

      it 'returns false for blank feature name' do
        expect(product.has_feature?("")).to be false
        expect(product.has_feature?(nil)).to be false
      end
    end

    describe 'features validation' do
      it 'validates features format' do
        product.features = "invalid"
        expect(product).not_to be_valid
        expect(product.errors[:features]).to include("must be an array")
      end

      it 'validates feature structure' do
        product.features = [{ "invalid" => "structure" }]
        expect(product).not_to be_valid
        expect(product.errors[:features]).to include("feature at index 0 must have a 'name' field")
      end

      it 'validates feature name presence' do
        product.features = [{ "name" => "" }]
        expect(product).not_to be_valid
        expect(product.errors[:features]).to include("feature at index 0 name cannot be blank")
      end
    end
  end
end