RSpec.shared_examples 'requires authentication' do
  it 'redirects unauthenticated users' do
    expect(response).to redirect_to(new_session_path)
  end
end

RSpec.shared_examples 'requires admin access' do
  it 'redirects non-admin users' do
    expect(response).to redirect_to(root_path)
  end
end

RSpec.shared_examples 'requires approved account' do
  it 'redirects users with unapproved accounts' do
    expect(response).to redirect_to(root_path)
  end
end

RSpec.shared_examples 'cacheable content' do
  it 'sets appropriate cache headers' do
    expect(response.headers['Cache-Control']).to be_present
  end
end

RSpec.shared_examples 'secure headers' do
  it 'includes security headers' do
    expect(response.headers['X-Frame-Options']).to eq('DENY')
    expect(response.headers['X-XSS-Protection']).to eq('1; mode=block')
    expect(response.headers['Content-Security-Policy']).to be_present
  end
end