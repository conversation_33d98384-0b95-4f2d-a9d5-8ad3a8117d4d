module AuthenticationHelpers
  def sign_in(user)
    session = user.sessions.create!
    cookies.signed[:session_id] = session.id
  end

  def sign_out
    cookies.delete(:session_id)
  end

  def create_authenticated_user(traits: [], **attributes)
    user = create(:user, *traits, **attributes)
    sign_in(user)
    user
  end

  def create_admin_session
    admin = create(:admin_user)
    sign_in(admin)
    admin
  end

  def create_government_user_session
    user = create(:user)
    account = create(:government_account, user: user)
    sign_in(user)
    [user, account]
  end

  def create_vendor_user_session
    user = create(:user)
    account = create(:vendor_account, user: user)
    sign_in(user)
    [user, account]
  end
end

RSpec.configure do |config|
  config.include AuthenticationHelpers, type: :request
  config.include AuthenticationHelpers, type: :controller
  config.include AuthenticationHelpers, type: :system
end