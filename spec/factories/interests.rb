FactoryBot.define do
  factory :interest do
    product
    government_account { association :account, account_type: 'government' }
    message { Faker::Lorem.paragraph(sentence_count: 3) }
    contact_name { Faker::Name.name }
    contact_email { Faker::Internet.email }
    contact_phone { Faker::PhoneNumber.phone_number }
    status { 'pending' }

    trait :reviewed do
      status { 'reviewed' }
      reviewed_at { 1.hour.ago }
    end

    trait :responded do
      status { 'responded' }
      reviewed_at { 2.hours.ago }
      responded_at { 1.hour.ago }
    end

    factory :reviewed_interest, traits: [:reviewed]
    factory :responded_interest, traits: [:responded]
  end
end