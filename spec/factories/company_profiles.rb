FactoryBot.define do
  factory :company_profile do
    account { association :account, account_type: 'vendor' }
    company_name { Faker::Company.name }
    company_size { ['1-10', '11-50', '51-200', '201-500', '500+'].sample }
    website { Faker::Internet.url }
    headquarters_location { Faker::Address.city }
    description { Faker::Company.catch_phrase }
    contact_email { Faker::Internet.email }
    published { true }

    trait :unpublished do
      published { false }
    end

    trait :with_rich_description do
      after(:build) do |company|
        company.description = Faker::Lorem.paragraphs(number: 3).join("\n\n")
      end
    end

    factory :unpublished_company_profile, traits: [:unpublished]
    factory :detailed_company_profile, traits: [:with_rich_description]
  end
end