FactoryBot.define do
  factory :product do
    account { association :account, account_type: 'vendor' }
    name { Faker::App.name }
    pricing_model { ['monthly', 'annual', 'one_time', 'custom', 'free'].sample }
    published { true }
    featured { false }
    features { [
      { "name" => "Cloud-based" },
      { "name" => "Mobile App" },
      { "name" => "API Integration" }
    ] }

    trait :unpublished do
      published { false }
    end

    trait :featured do
      featured { true }
    end

    trait :with_description do
      after(:build) do |product|
        product.description = Faker::Lorem.paragraphs(number: 2).join("\n\n")
      end
    end

    trait :with_custom_features do
      features { [
        { "name" => Faker::Lorem.word.capitalize },
        { "name" => Faker::Lorem.word.capitalize },
        { "name" => Faker::Lorem.word.capitalize }
      ] }
    end

    factory :unpublished_product, traits: [:unpublished]
    factory :featured_product, traits: [:featured, :with_description]
    factory :detailed_product, traits: [:with_description, :with_custom_features]
  end
end