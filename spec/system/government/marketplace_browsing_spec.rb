require 'rails_helper'

RSpec.describe "Government Marketplace Browsing", type: :system do
  let(:government_user) { create(:user) }
  let(:government_account) { create(:government_account, user: government_user) }
  let!(:cloud_category) { create(:cloud_infrastructure_category) }
  let!(:analytics_category) { create(:data_analytics_category) }
  let!(:cloud_product) { create(:detailed_product, published: true) }
  let!(:analytics_product) { create(:detailed_product, published: true) }

  before do
    cloud_product.categories << cloud_category
    analytics_product.categories << analytics_category
    
    # Sign in as government user
    visit new_session_path
    fill_in "Email address", with: government_user.email_address
    fill_in "Password", with: "password123"
    click_button "Sign in"
  end

  describe "Marketplace home page" do
    before do
      visit marketplace_path
    end

    it "displays the main marketplace interface" do
      expect(page).to have_content("Discover Government Technology Solutions")
      expect(page).to have_content("Browse by Category")
    end

    it "shows category navigation" do
      expect(page).to have_content(cloud_category.name)
      expect(page).to have_content(analytics_category.name)
    end

    it "displays featured products" do
      featured_product = create(:featured_product, published: true)
      
      visit marketplace_path
      
      expect(page).to have_content("Featured Products")
      expect(page).to have_content(featured_product.name)
    end
  end

  describe "Product search functionality" do
    before do
      visit marketplace_path
    end

    it "allows searching for products" do
      fill_in "Search products, companies, or solutions...", with: cloud_product.name
      click_button "Search"

      expect(page).to have_content(cloud_product.name)
      expect(page).not_to have_content(analytics_product.name)
    end

    it "handles empty search results gracefully" do
      fill_in "Search products, companies, or solutions...", with: "nonexistent product"
      click_button "Search"

      expect(page).to have_content("No products found")
    end
  end

  describe "Category filtering" do
    before do
      visit marketplace_products_path
    end

    it "filters products by category" do
      click_link cloud_category.name

      expect(page).to have_content(cloud_product.name)
      expect(page).not_to have_content(analytics_product.name)
    end
  end

  describe "Product details page" do
    before do
      visit marketplace_product_path(cloud_product)
    end

    it "displays comprehensive product information" do
      expect(page).to have_content(cloud_product.name)
      expect(page).to have_content("Product Overview")
      expect(page).to have_content("Express Interest")
    end

    it "shows product categories" do
      expect(page).to have_content(cloud_category.name)
    end

    it "displays company information" do
      company = cloud_product.account.company_profile
      if company
        expect(page).to have_content(company.company_name)
      end
    end
  end

  describe "Express interest workflow" do
    before do
      visit marketplace_product_path(cloud_product)
    end

    it "allows submitting interest form" do
      click_button "Express Interest"

      fill_in "Message", with: "We are interested in this solution for our department"
      fill_in "Contact Name", with: "Jane Smith"
      fill_in "Contact Email", with: "<EMAIL>"
      fill_in "Contact Phone", with: "555-1234"

      click_button "Submit Interest"

      expect(page).to have_content("Your interest has been submitted")
    end

    it "validates required fields" do
      click_button "Express Interest"
      click_button "Submit Interest"

      expect(page).to have_content("can't be blank")
    end
  end

  describe "Interest tracking" do
    let!(:user_interest) { create(:interest, government_account: government_account, product: cloud_product) }

    before do
      visit government_interests_path
    end

    it "displays user's submitted interests" do
      expect(page).to have_content("Your Interests")
      expect(page).to have_content(cloud_product.name)
      expect(page).to have_content(user_interest.message.truncate(50))
    end

    it "shows interest status" do
      expect(page).to have_content(user_interest.status.humanize)
    end
  end

  describe "Responsive design" do
    before do
      visit marketplace_path
    end

    it "adapts to mobile viewport", js: true do
      page.driver.browser.manage.window.resize_to(375, 667) # iPhone size

      expect(page).to have_css('.grid') # Ensure grid layout is present
      expect(page).to have_content("Discover Government Technology Solutions")
    end
  end

  describe "Accessibility features" do
    before do
      visit marketplace_path
    end

    it "includes proper heading structure" do
      expect(page).to have_css('h1')
      expect(page).to have_css('h2')
    end

    it "provides descriptive link text" do
      expect(page).to have_link("View all products")
      expect(page).to have_link("View all companies")
    end
  end
end