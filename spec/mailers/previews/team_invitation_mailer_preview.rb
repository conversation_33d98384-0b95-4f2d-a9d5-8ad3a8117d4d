# Preview all emails at http://localhost:3000/rails/mailers/team_invitation_mailer
class TeamInvitationMailerPreview < ActionMailer::Preview
  def invitation_email
    user = User.new(first_name: "<PERSON>", last_name: "<PERSON><PERSON>", email_address: "<EMAIL>")
    account = Account.new(name: "Acme Corp", account_type: "vendor")
    account.owner = user
    
    invited_by = User.new(first_name: "<PERSON>", last_name: "<PERSON>", email_address: "<EMAIL>")
    
    invitation = TeamInvitation.new(
      email: "<EMAIL>",
      role: "admin",
      message: "Welcome to our team! We're excited to have you join us and contribute to our innovative projects.",
      token: SecureRandom.urlsafe_base64(32),
      expires_at: 7.days.from_now
    )
    invitation.account = account
    invitation.invited_by = invited_by
    
    TeamInvitationMailer.invitation_email(invitation)
  end
end
