# Preview all emails at http://localhost:3000/rails/mailers/user_mailer
class UserMailerPreview < ActionMailer::Preview
  def welcome_with_confirmation
    user = User.new(first_name: "<PERSON>", last_name: "<PERSON><PERSON>", email_address: "<EMAIL>")
    account = Account.new(name: "<PERSON>'s Vendor Account", account_type: "vendor", status: "pending")
    account.owner = user
    account.confirmation_token = SecureRandom.urlsafe_base64(32)
    UserMailer.welcome(user, account)
  end

  def welcome_without_confirmation
    user = User.new(first_name: "<PERSON>", last_name: "<PERSON>", email_address: "<EMAIL>")
    account = Account.new(name: "<PERSON>'s Agency Account", account_type: "agency", status: "pending")
    account.owner = user
    account.confirmation_token = nil
    UserMailer.welcome(user, account)
  end
end