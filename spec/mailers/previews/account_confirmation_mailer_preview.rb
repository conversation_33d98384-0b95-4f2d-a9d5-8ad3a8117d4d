class AccountConfirmationMailerPreview < ActionMailer::Preview
  def self.emails
    [:confirmation_email]
  end

  def self.description
    "Account confirmation email notifications"
  end

  def confirmation_email
    user = User.new(first_name: "<PERSON>", last_name: "<PERSON><PERSON>", email_address: "<EMAIL>")
    account = Account.new(name: "<PERSON>'s Vendor Account", account_type: "vendor", status: "pending")
    account.owner = user
    account.confirmation_token = SecureRandom.urlsafe_base64(32)
    AccountConfirmationMailer.confirmation_email(account)
  end
end
