require "test_helper"

class JsonFeaturesTest < ActionDispatch::IntegrationTest
  def setup
    @user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON><PERSON>"
    )
    
    @account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      owner: @user
    )
    
    @category = Category.create!(name: "Test Category")
  end

  test "product can be created with JSON features" do
    product = Product.create!(
      name: "Test Product",
      account: @account,
      category: @category,
      features: [
        { "name" => "Cloud-based" },
        { "name" => "Mobile App" },
        { "name" => "API Integration" }
      ]
    )
    
    assert product.persisted?
    assert_equal 3, product.features.count
    assert_equal ["Cloud-based", "Mobile App", "API Integration"], product.feature_names
  end

  test "product feature helper methods work correctly" do
    product = Product.create!(
      name: "Test Product",
      account: @account,
      category: @category,
      features: []
    )
    
    # Test adding features
    product.add_feature("New Feature")
    assert product.has_feature?("New Feature")
    assert_equal ["New Feature"], product.feature_names
    
    # Test removing features
    product.remove_feature("New Feature")
    assert_not product.has_feature?("New Feature")
    assert_equal [], product.feature_names
  end

  test "product validates features format" do
    product = Product.new(
      name: "Test Product",
      account: @account,
      category: @category,
      features: "invalid"
    )
    
    assert_not product.valid?
    assert_includes product.errors[:features], "must be an array"
  end

  test "product validates feature structure" do
    product = Product.new(
      name: "Test Product",
      account: @account,
      category: @category,
      features: [{ "invalid" => "structure" }]
    )
    
    assert_not product.valid?
    assert_includes product.errors[:features], "feature at index 0 must have a 'name' field"
  end
end
