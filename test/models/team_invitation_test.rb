require "test_helper"

class TeamInvitationTest < ActiveSupport::TestCase
  def setup
    @user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "Inviter",
      confirmed_at: 1.week.ago
    )
    
    @account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      owner: @user,
      approved_at: 1.week.ago
    )
    
    @invitation = TeamInvitation.new(
      account: @account,
      invited_by: @user,
      email: "<EMAIL>",
      role: "member"
    )
  end

  test "should be valid with valid attributes" do
    assert @invitation.valid?
  end

  test "should require email" do
    @invitation.email = nil
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:email], "can't be blank"
  end

  test "should require valid email format" do
    @invitation.email = "invalid_email"
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:email], "is invalid"
  end

  test "should require valid role" do
    @invitation.role = "invalid"
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:role], "is not included in the list"
  end

  test "should generate token and expiry on create" do
    @invitation.save!
    assert_not_nil @invitation.token
    assert_not_nil @invitation.expires_at
    assert @invitation.expires_at > Time.current
  end

  test "should not allow duplicate email for same account" do
    @invitation.save!
    
    duplicate = TeamInvitation.new(
      account: @account,
      invited_by: @user,
      email: "<EMAIL>",
      role: "admin"
    )
    
    assert_not duplicate.valid?
    assert_includes duplicate.errors[:email], "has already been invited to this account"
  end

  test "should check if expired" do
    @invitation.expires_at = 1.day.ago
    assert @invitation.expired?
    
    @invitation.expires_at = 1.day.from_now
    assert_not @invitation.expired?
  end
end
