require "test_helper"

class ProductTest < ActiveSupport::TestCase
  def setup
    @user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON><PERSON>",
      confirmed_at: 1.week.ago
    )
    
    @account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      approved_at: 1.week.ago,
      owner: @user
    )
    
    @product = Product.new(
      name: "Test Product",
      account: @account,
      pricing_model: "monthly"
    )
  end

  test "should be valid with valid attributes" do
    assert @product.valid?
  end

  test "should require name" do
    @product.name = nil
    assert_not @product.valid?
    assert_includes @product.errors[:name], "can't be blank"
  end

  test "should require account" do
    @product.account = nil
    assert_not @product.valid?
    assert_includes @product.errors[:account_id], "can't be blank"
  end

  test "should generate slug from name" do
    @product.save!
    assert_not_nil @product.slug
    assert_equal "test-product", @product.slug
  end

  test "should handle JSON features" do
    features = [
      { "name" => "Cloud-based" },
      { "name" => "Mobile App" },
      { "name" => "API Integration" }
    ]
    @product.features = features
    @product.save!

    assert_equal features, @product.features
    assert_equal ["Cloud-based", "Mobile App", "API Integration"], @product.feature_names
  end

  test "should add and remove features" do
    @product.features = []
    @product.add_feature("New Feature")
    assert_equal [{ "name" => "New Feature" }], @product.features

    @product.remove_feature("New Feature")
    assert_equal [], @product.features
  end

  test "should check if feature exists" do
    @product.features = [{ "name" => "Test Feature" }]
    assert @product.has_feature?("Test Feature")
    assert_not @product.has_feature?("Non-existent Feature")
  end

  test "should check published status" do
    @product.published = false
    assert_not @product.published?
    
    @product.published = true
    assert @product.published?
  end

  test "should have scope for published products" do
    @product.published = true
    @product.save!
    
    unpublished_product = Product.create!(
      name: "Unpublished Product",
      account: @account,
      published: false
    )
    
    published_products = Product.published
    assert_includes published_products, @product
    assert_not_includes published_products, unpublished_product
  end

  test "should associate with category" do
    category = Category.create!(name: "Test Category")

    @product.category = category
    @product.save!

    assert_equal category, @product.category
  end
end
