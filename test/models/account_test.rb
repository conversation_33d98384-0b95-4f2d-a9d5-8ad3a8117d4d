require "test_helper"

class AccountTest < ActiveSupport::TestCase
  def setup
    @user = users(:one)
    @account = Account.new(
      name: "Tech Solutions Inc",
      account_type: "vendor",
      status: "pending",
      owner: @user
    )
  end

  test "should be valid with valid attributes" do
    assert @account.valid?
  end

  test "should require name" do
    @account.name = nil
    assert_not @account.valid?
    assert_includes @account.errors[:name], "can't be blank"
  end

  test "should require valid account type" do
    assert_raises(ArgumentError) do
      @account.account_type = "invalid"
    end
  end

  test "should require valid status" do
    assert_raises(ArgumentError) do
      @account.status = "invalid"
    end
  end

  test "should check if vendor" do
    @account.account_type = "vendor"
    assert @account.vendor?
    assert_not @account.agency?
  end

  test "should check if agency" do
    @account.account_type = "agency"
    assert @account.agency?
    assert_not @account.vendor?
  end

  test "should check if approved" do
    @account.status = "approved"
    assert @account.approved?
    
    @account.status = "pending"
    assert_not @account.approved?
  end

  test "should check status methods" do
    @account.status = "pending"
    assert @account.pending?
    assert_not @account.approved?
    assert_not @account.rejected?
    
    @account.status = "approved"
    assert @account.approved?
    assert_not @account.pending?
    assert_not @account.rejected?
    
    @account.status = "rejected"
    assert @account.rejected?
    assert_not @account.pending?
    assert_not @account.approved?
  end

  test "should approve account" do
    admin_user = users(:admin)
    @account.save!
    
    @account.approve!(admin_user)
    
    assert @account.approved?
    assert_equal admin_user, @account.approved_by
    assert_not_nil @account.approved_at
  end

  test "should reject account" do
    admin_user = users(:admin)
    @account.save!
    
    @account.reject!(admin_user)
    
    assert @account.rejected?
    assert_equal admin_user, @account.approved_by
    assert_nil @account.approved_at
  end

  test "should generate slug from name" do
    @account.save!
    assert_not_nil @account.slug
    assert_equal "tech-solutions-inc", @account.slug
  end
end
