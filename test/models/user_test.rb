require "test_helper"

class UserTest < ActiveSupport::TestCase
  def setup
    @user = User.new(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>"
    )
  end

  test "should be valid with valid attributes" do
    assert @user.valid?
  end

  test "should require email address" do
    @user.email_address = nil
    assert_not @user.valid?
    assert_includes @user.errors[:email_address], "can't be blank"
  end

  test "should require first name" do
    @user.first_name = nil
    assert_not @user.valid?
    assert_includes @user.errors[:first_name], "can't be blank"
  end

  test "should require last name" do
    @user.last_name = nil
    assert_not @user.valid?
    assert_includes @user.errors[:last_name], "can't be blank"
  end

  test "should normalize email address" do
    @user.email_address = "  <EMAIL>  "
    @user.save!
    assert_equal "<EMAIL>", @user.email_address
  end

  test "should return full name" do
    assert_equal "<PERSON>", @user.full_name
  end

  test "should check if confirmed" do
    assert_not @user.confirmed?
    @user.confirmed_at = Time.current
    assert @user.confirmed?
  end

  test "should check if approved based on account status" do
    @user.save!
    assert_not @user.approved?
    
    # Create an approved account for the user
    account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      owner: @user
    )
    AccountUser.create!(account: account, user: @user, role: "admin")
    
    # Reload user to get fresh associations
    @user.reload
    assert @user.approved?
  end

  test "should check super admin status" do
    assert_not @user.super_admin?
    assert_not @user.admin?
    
    @user.super_admin = true
    assert @user.super_admin?
    assert @user.admin?
  end

  test "should have scope for super admins" do
    @user.super_admin = true
    @user.save!
    
    regular_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Regular",
      last_name: "User",
      super_admin: false
    )
    
    super_admins = User.super_admins
    assert_includes super_admins, @user
    assert_not_includes super_admins, regular_user
  end
end
