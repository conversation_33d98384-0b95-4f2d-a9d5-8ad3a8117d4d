require "test_helper"

class CompanyProfileTest < ActiveSupport::TestCase
  def setup
    @user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON><PERSON>",
      confirmed_at: 1.week.ago
    )
    
    @account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      approved_at: 1.week.ago,
      owner: @user
    )
    
    @company_profile = CompanyProfile.new(
      account: @account,
      company_name: "Test Tech Solutions",
      website: "https://testtech.com",
      contact_email: "<EMAIL>"
    )
  end

  test "should be valid with valid attributes" do
    assert @company_profile.valid?
  end

  test "should require company name" do
    @company_profile.company_name = nil
    assert_not @company_profile.valid?
    assert_includes @company_profile.errors[:company_name], "can't be blank"
  end

  test "should require unique account" do
    @company_profile.save!
    
    duplicate = CompanyProfile.new(
      account: @account,
      company_name: "Another Company"
    )
    
    assert_not duplicate.valid?
    assert_includes duplicate.errors[:account_id], "has already been taken"
  end

  test "should generate slug from company name" do
    @company_profile.save!
    assert_not_nil @company_profile.slug
    assert_equal "test-tech-solutions", @company_profile.slug
  end

  test "should check published status" do
    @company_profile.published = false
    assert_not @company_profile.published?
    
    @company_profile.published = true
    assert @company_profile.published?
  end

  test "should handle service areas as array" do
    areas = ["Government IT", "Cloud Solutions", "Data Analytics"]
    @company_profile.service_areas_array = areas
    @company_profile.save!
    
    assert_equal areas, @company_profile.service_areas_array
  end

  test "should handle certifications as array" do
    certs = ["SOC 2", "ISO 27001", "FedRAMP"]
    @company_profile.certifications_array = certs
    @company_profile.save!
    
    assert_equal certs, @company_profile.certifications_array
  end

  test "should have scope for published profiles" do
    @company_profile.published = true
    @company_profile.save!
    
    user2 = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Jane",
      last_name: "Vendor",
      confirmed_at: 1.week.ago
    )
    
    account2 = Account.create!(
      name: "Another Company",
      account_type: "vendor",
      status: "approved",
      approved_at: 1.week.ago,
      owner: user2
    )
    
    unpublished_profile = CompanyProfile.create!(
      account: account2,
      company_name: "Unpublished Company",
      published: false
    )
    
    published_profiles = CompanyProfile.published
    assert_includes published_profiles, @company_profile
    assert_not_includes published_profiles, unpublished_profile
  end
end
