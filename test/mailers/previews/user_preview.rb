class UserPreview < ActionMailer::Preview
  def self.emails
    [:welcome, :account_approved, :account_rejected, :password_reset]
  end

  def self.description
    "User account related email notifications"
  end

  def welcome
    UserMailer.welcome(sample_user, sample_account_pending)
  end

  def account_approved
    UserMailer.account_approved(sample_user_with_vendor_account)
  end

  def account_rejected
    UserMailer.account_rejected(sample_user, "Account information could not be verified")
  end

  def password_reset
    UserMailer.password_reset(sample_user, "abc123def456")
  end

  private

  def sample_user
    User.new(
      id: 1,
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>", 
      email_address: "<EMAIL>",
      created_at: 2.days.ago,
      confirmed_at: 1.day.ago
    )
  end

  def sample_user_with_vendor_account
    user = sample_user
    account = Account.new(
      id: 1,
      name: "TechCorp Solutions",
      account_type: "vendor",
      status: "approved"
    )
    user.define_singleton_method(:accounts) { [account] }
    user
  end

  def sample_account_pending
    Account.new(
      id: 1,
      name: "<PERSON>'s Vendor Account",
      account_type: "vendor",
      status: "pending",
      confirmation_token: SecureRandom.urlsafe_base64(32),
      confirmation_sent_at: Time.current,
      confirmed_at: nil,
      created_at: 1.hour.ago
    )
  end
end