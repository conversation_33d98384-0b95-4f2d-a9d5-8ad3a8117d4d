class AdminPreview < ActionMailer::Preview
  def self.emails
    [:new_user_signup, :new_account_pending, :system_alert, :weekly_report]
  end

  def self.description
    "Administrative email notifications"
  end

  def new_user_signup
    user = sample_user
    account = sample_account
    user.define_singleton_method(:owned_accounts) { [account] }
    AdminMailer.new_user_signup(user)
  end

  def new_account_pending
    AdminMailer.new_account_pending(sample_account)
  end

  def system_alert
    AdminMailer.system_alert(
      "Database Performance Issue",
      "Database response times are elevated above normal thresholds",
      {
        average_response_time: "2.3s",
        threshold: "1.0s", 
        affected_queries: ["user_login", "product_search"],
        timestamp: Time.current,
        server: "web-01"
      }
    )
  end

  def weekly_report
    AdminMailer.weekly_report(1.week.ago.to_date, Date.current)
  end

  private

  def sample_user
    User.new(
      id: 1,
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      email_address: "<EMAIL>",
      created_at: 2.hours.ago,
      confirmed_at: 1.hour.ago
    )
  end

  def sample_account
    user = sample_user
    account = Account.new(
      id: 1,
      name: "City of Los Angeles",
      account_type: "agency",
      status: "pending",
      created_at: 1.hour.ago,
      approved_at: nil
    )
    
    account.define_singleton_method(:owner) { user }
    account
  end
end