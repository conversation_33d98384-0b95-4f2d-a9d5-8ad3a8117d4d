class LeadPreview < ActionMailer::Preview
  def self.emails
    [:new_lead_notification, :lead_status_update, :lead_response]
  end

  def self.description
    "Lead and inquiry related email notifications"
  end

  def new_lead_notification
    LeadMailer.new_lead_notification(sample_lead)
  end

  def lead_status_update
    lead = sample_lead
    lead.status = 'contacted'
    LeadMailer.lead_status_update(lead)
  end

  def lead_response
    LeadMailer.lead_response(sample_lead, "Thank you for your interest! We'd love to schedule a demo to show you how our solution can help your agency...")
  end

  private

  def sample_lead
    # Create mock objects for preview
    vendor_account = Account.new(
      id: 1,
      name: "TechCorp Solutions",
      account_type: "vendor",
      status: "approved"
    )

    government_account = Account.new(
      id: 2, 
      name: "City of San Francisco",
      account_type: "government",
      status: "approved"
    )

    product = Product.new(
      id: 1,
      name: "CitizenConnect Portal",
      description: "Modern citizen engagement platform"
    )

    vendor_user = User.new(
      id: 1,
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      email_address: "<EMAIL>"
    )

    government_user = User.new(
      id: 2,
      first_name: "<PERSON>",
      last_name: "<PERSON>", 
      email_address: "<EMAIL>"
    )

    company_profile = CompanyProfile.new(
      company_name: "TechCorp Solutions"
    )

    lead = Lead.new(
      id: 1,
      contact_name: "<PERSON> <PERSON>",
      contact_email: "<EMAIL>",
      contact_phone: "(*************",
      timeline: "Q2 2024",
      budget_range: "$50,000 - $100,000",
      message: "We're looking for a modern citizen engagement platform to improve our digital services. Would love to learn more about your solution and see a demo.",
      status: "pending",
      created_at: 2.hours.ago,
      updated_at: 1.hour.ago
    )

    # Set up associations
    lead.define_singleton_method(:product) { product }
    lead.define_singleton_method(:account) { vendor_account }
    lead.define_singleton_method(:government_account) { government_account }
    
    vendor_account.define_singleton_method(:owner) { vendor_user }
    vendor_account.define_singleton_method(:company_profile) { company_profile }
    government_account.define_singleton_method(:owner) { government_user }

    lead
  end
end