<% password_digest = BCrypt::Password.create("password") %>

one:
  email_address: <EMAIL>
  password_digest: <%= password_digest %>
  first_name: <PERSON>
  last_name: <PERSON><PERSON>
  confirmed_at: <%= 1.week.ago %>

two:
  email_address: <EMAIL>
  password_digest: <%= password_digest %>
  first_name: <PERSON>
  last_name: <PERSON>
  confirmed_at: <%= 1.week.ago %>

admin:
  email_address: <EMAIL>
  password_digest: <%= password_digest %>
  first_name: Admin
  last_name: User
  confirmed_at: <%= 1.month.ago %>
  super_admin: true
