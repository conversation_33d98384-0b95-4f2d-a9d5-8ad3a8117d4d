# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  account: one
  name: Test Product One
  slug: test-product-one
  pricing_model: monthly
  published: false
  features: '[{"name": "Cloud-based"}, {"name": "Mobile App"}]'

two:
  account: two
  name: Test Product Two
  slug: test-product-two
  pricing_model: annual
  published: false
  features: '[{"name": "API Integration"}, {"name": "Analytics"}]'
