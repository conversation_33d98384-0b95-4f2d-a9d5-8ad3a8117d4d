# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  account: one
  invited_by: one
  email: <EMAIL>
  role: member
  status: pending
  message: Welcome to our team!
  token: unique_token_abc123
  expires_at: <%= 7.days.from_now %>

two:
  account: two
  invited_by: two
  email: <EMAIL>
  role: admin
  status: pending
  message: Join our organization!
  token: unique_token_def456
  expires_at: <%= 7.days.from_now %>
