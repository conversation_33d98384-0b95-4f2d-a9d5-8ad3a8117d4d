# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  name: TechCorp Solutions
  account_type: vendor
  status: approved
  approved_at: <%= 1.week.ago %>
  approved_by: admin
  owner: one
  slug: techcorp-solutions

two:
  name: City of Springfield
  account_type: agency
  status: approved
  approved_at: <%= 1.week.ago %>
  approved_by: admin
  owner: two
  slug: city-of-springfield

vendor_account:
  name: TechCorp Solutions
  account_type: vendor
  status: approved
  approved_at: <%= 1.week.ago %>
  approved_by: admin
  owner: one
  slug: techcorp-solutions-2

government_account:
  name: City of Springfield
  account_type: agency
  status: approved
  approved_at: <%= 1.week.ago %>
  approved_by: admin
  owner: two
  slug: city-of-springfield-2

pending_vendor:
  name: StartupTech Inc
  account_type: vendor
  status: pending
  owner: one
  slug: startuptech-inc

rejected_vendor:
  name: RejectedCorp
  account_type: vendor
  status: rejected
  approved_by: admin
  owner: one
  slug: rejectedcorp
