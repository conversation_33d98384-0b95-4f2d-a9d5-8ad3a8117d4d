require "test_helper"

class Government::DashboardControllerTest < ActionDispatch::IntegrationTest
  def setup
    @gov_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Government",
      last_name: "User",
      confirmed_at: 1.week.ago
    )
    
    @gov_account = Account.create!(
      name: "City Government",
      account_type: "government",
      status: "approved",
      owner: @gov_user,
      approved_at: 1.week.ago
    )
    
    AccountUser.create!(
      account: @gov_account,
      user: @gov_user,
      role: "admin",
      joined_at: 1.week.ago
    )
    
    @vendor_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>endor",
      last_name: "User",
      confirmed_at: 1.week.ago
    )
    
    @vendor_account = Account.create!(
      name: "Tech Vendor",
      account_type: "vendor",
      status: "approved",
      owner: @vendor_user,
      approved_at: 1.week.ago
    )
    
    @company_profile = CompanyProfile.create!(
      account: @vendor_account,
      company_name: "TechCorp Solutions",
      published: true
    )
    
    @product = Product.create!(
      name: "Test Product",
      account: @vendor_account,
      pricing_model: "monthly",
      published: true
    )
    
    # Create some leads with different statuses
    @pending_lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: @gov_account,
      user: @gov_user,
      contact_name: "John Pending",
      contact_email: "<EMAIL>",
      status: "pending",
      source: "marketplace"
    )
    
    @contacted_lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: @gov_account,
      user: @gov_user,
      contact_name: "John Contacted",
      contact_email: "<EMAIL>",
      status: "contacted",
      source: "marketplace"
    )
    
    @qualified_lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: @gov_account,
      user: @gov_user,
      contact_name: "John Qualified",
      contact_email: "<EMAIL>",
      status: "qualified",
      source: "marketplace"
    )
    
    # Sign in the government user
    post session_path, params: { 
      email_address: @gov_user.email_address, 
      password: "password123" 
    }
  end

  test "should get dashboard index" do
    get government_root_path
    assert_response :success
    assert_includes response.body, "Dashboard"
    assert_includes response.body, "Total Interests"
    assert_includes response.body, "Pending"
    assert_includes response.body, "Contacted"
    assert_includes response.body, "Qualified"
  end

  test "should show correct interest statistics" do
    get government_dashboard_path
    assert_response :success
    
    # Should show 3 total interests (1 pending, 1 contacted, 1 qualified)
    assert_match /Total Interests.*3/, response.body.gsub(/\s+/, ' ')
    assert_match /Pending.*1/, response.body.gsub(/\s+/, ' ')
    assert_match /Contacted.*1/, response.body.gsub(/\s+/, ' ')
    assert_match /Qualified.*1/, response.body.gsub(/\s+/, ' ')
  end

  test "should show recent interests" do
    get government_dashboard_path
    assert_response :success
    assert_includes response.body, "Recent Interests"
    assert_includes response.body, @pending_lead.contact_name
    assert_includes response.body, @contacted_lead.contact_name
    assert_includes response.body, @qualified_lead.contact_name
    assert_includes response.body, @product.name
  end

  test "should limit recent interests to 5" do
    # Create more than 5 leads
    6.times do |i|
      Lead.create!(
        product: @product,
        account: @vendor_account,
        government_account: @gov_account,
        user: @gov_user,
        contact_name: "Extra Lead #{i}",
        contact_email: "extra#{i}@gov.com",
        status: "pending",
        source: "marketplace"
      )
    end
    
    get government_dashboard_path
    assert_response :success
    
    # Should only show the 5 most recent
    recent_leads = @gov_account.leads.order(created_at: :desc).limit(5)
    recent_leads.each do |lead|
      assert_includes response.body, lead.contact_name
    end
  end

  test "should show featured companies and products" do
    get government_dashboard_path
    assert_response :success
    assert_includes response.body, @company_profile.company_name
    assert_includes response.body, @product.name
  end

  test "should show quick actions" do
    get government_dashboard_path
    assert_response :success
    assert_includes response.body, "Quick Actions"
    assert_includes response.body, "Browse Marketplace"
    assert_includes response.body, "Find Companies"
    assert_includes response.body, "Search Products"
    assert_includes response.body, "Update Profile"
  end

  test "should handle dashboard with no interests" do
    # Remove all leads
    Lead.destroy_all
    
    get government_dashboard_path
    assert_response :success
    assert_includes response.body, "No interests yet"
    assert_includes response.body, "Browse Products"
  end

  test "should only show interests for current government account" do
    # Create another government account with leads
    other_gov_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Other",
      last_name: "Gov",
      confirmed_at: 1.week.ago
    )
    
    other_gov_account = Account.create!(
      name: "Other City",
      account_type: "government",
      status: "approved",
      owner: other_gov_user,
      approved_at: 1.week.ago
    )
    
    other_lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: other_gov_account,
      user: other_gov_user,
      contact_name: "Other User",
      contact_email: "<EMAIL>",
      status: "pending",
      source: "marketplace"
    )
    
    get government_dashboard_path
    assert_response :success
    
    # Should show current account's leads but not other account's
    assert_includes response.body, @pending_lead.contact_name
    assert_not_includes response.body, "Other User"
    
    # Stats should only count current account's leads
    assert_match /Total Interests.*3/, response.body.gsub(/\s+/, ' ')
  end

  test "should require government authentication" do
    delete session_path
    
    get government_dashboard_path
    assert_redirected_to new_session_path
  end

  test "should require government account type" do
    # Sign out and sign in as vendor
    delete session_path
    
    post session_path, params: { 
      email_address: @vendor_user.email_address, 
      password: "password123" 
    }
    
    get government_dashboard_path
    assert_redirected_to root_path
  end

  test "should show status indicators correctly" do
    get government_dashboard_path
    assert_response :success
    
    # Check for status-specific styling
    assert_includes response.body, "bg-yellow-100 text-yellow-800"  # Pending
    assert_includes response.body, "bg-blue-100 text-blue-800"      # Contacted
    assert_includes response.body, "bg-green-100 text-green-800"    # Qualified
  end

  test "should provide navigation to all interests" do
    get government_dashboard_path
    assert_response :success
    assert_includes response.body, "View all interests"
    
    # Check the link goes to the right path
    assert_select "a[href='#{government_interests_path}']"
  end

  test "should handle empty featured content gracefully" do
    # Remove all published content
    CompanyProfile.update_all(published: false)
    Product.update_all(published: false)
    
    get government_dashboard_path
    assert_response :success
    # Should still render without errors
  end
end