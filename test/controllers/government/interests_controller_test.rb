require "test_helper"

class Government::InterestsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @gov_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "User",
      confirmed_at: 1.week.ago
    )
    
    @gov_account = Account.create!(
      name: "City Government",
      account_type: "government",
      status: "approved",
      owner: @gov_user,
      approved_at: 1.week.ago
    )
    
    AccountUser.create!(
      account: @gov_account,
      user: @gov_user,
      role: "admin",
      joined_at: 1.week.ago
    )
    
    @vendor_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>endor",
      last_name: "User",
      confirmed_at: 1.week.ago
    )
    
    @vendor_account = Account.create!(
      name: "Tech Vendor",
      account_type: "vendor",
      status: "approved",
      owner: @vendor_user,
      approved_at: 1.week.ago
    )
    
    @product = Product.create!(
      name: "Test Product",
      description: "A great product for testing",
      account: @vendor_account,
      pricing_model: "monthly",
      published: true
    )
    
    # Sign in the government user
    post session_path, params: { 
      email_address: @gov_user.email_address, 
      password: "password123" 
    }
  end

  test "should create interest successfully" do
    assert_difference('Lead.count') do
      post marketplace_product_interests_path(@product), params: {
        lead: {
          contact_name: "John Doe",
          contact_email: "<EMAIL>",
          contact_phone: "555-1234",
          timeline: "immediate",
          budget_range: "10k_50k",
          message: "We need this solution urgently"
        }
      }
    end
    
    assert_redirected_to marketplace_product_path(@product)
    follow_redirect!
    assert_includes response.body, "Your interest has been submitted"
    
    lead = Lead.last
    assert_equal @product, lead.product
    assert_equal @vendor_account, lead.account
    assert_equal @gov_account, lead.government_account
    assert_equal @gov_user, lead.user
    assert_equal "marketplace", lead.source
    assert_equal "pending", lead.status
    assert_equal "John Doe", lead.contact_name
    assert_equal "<EMAIL>", lead.contact_email
    assert_equal "immediate", lead.timeline
    assert_equal "10k_50k", lead.budget_range
  end

  test "should handle interest creation errors" do
    # Create with invalid data
    assert_no_difference('Lead.count') do
      post marketplace_product_interests_path(@product), params: {
        lead: {
          contact_name: "",
          contact_email: "invalid-email"
        }
      }
    end
    
    assert_redirected_to marketplace_product_path(@product)
    follow_redirect!
    assert_includes response.body, "There was an error"
  end

  test "should get interests index" do
    # Create some leads for this government account
    lead1 = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: @gov_account,
      user: @gov_user,
      contact_name: "John Doe",
      contact_email: "<EMAIL>",
      status: "pending",
      source: "marketplace"
    )
    
    lead2 = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: @gov_account,
      user: @gov_user,
      contact_name: "Jane Smith",
      contact_email: "<EMAIL>",
      status: "contacted",
      source: "marketplace"
    )
    
    get government_interests_path
    assert_response :success
    assert_includes response.body, "My Product Interests"
    assert_includes response.body, lead1.contact_name
    assert_includes response.body, lead2.contact_name
    assert_includes response.body, @product.name
  end

  test "should show empty state when no interests" do
    get government_interests_path
    assert_response :success
    assert_includes response.body, "No interests yet"
    assert_includes response.body, "Browse Products"
  end

  test "should only show interests for current government account" do
    # Create another government account and lead
    other_gov_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Other",
      last_name: "Gov",
      confirmed_at: 1.week.ago
    )
    
    other_gov_account = Account.create!(
      name: "Other City",
      account_type: "government",
      status: "approved",
      owner: other_gov_user,
      approved_at: 1.week.ago
    )
    
    other_lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: other_gov_account,
      user: other_gov_user,
      contact_name: "Other User",
      contact_email: "<EMAIL>",
      status: "pending",
      source: "marketplace"
    )
    
    my_lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: @gov_account,
      user: @gov_user,
      contact_name: "My User",
      contact_email: "<EMAIL>",
      status: "pending",
      source: "marketplace"
    )
    
    get government_interests_path
    assert_response :success
    assert_includes response.body, "My User"
    assert_not_includes response.body, "Other User"
  end

  test "should require authentication" do
    delete session_path
    
    post marketplace_product_interests_path(@product), params: {
      lead: { contact_name: "Test" }
    }
    assert_redirected_to new_session_path
    
    get government_interests_path
    assert_redirected_to new_session_path
  end

  test "should require government account" do
    # Sign out and sign in as vendor
    delete session_path
    
    post session_path, params: { 
      email_address: @vendor_user.email_address, 
      password: "password123" 
    }
    
    get government_interests_path
    assert_redirected_to root_path
  end

  test "should show interest status timeline correctly" do
    # Create leads with different statuses
    pending_lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: @gov_account,
      user: @gov_user,
      contact_name: "Pending User",
      contact_email: "<EMAIL>",
      status: "pending",
      source: "marketplace"
    )
    
    contacted_lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: @gov_account,
      user: @gov_user,
      contact_name: "Contacted User",
      contact_email: "<EMAIL>",
      status: "contacted",
      source: "marketplace"
    )
    
    qualified_lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      government_account: @gov_account,
      user: @gov_user,
      contact_name: "Qualified User",
      contact_email: "<EMAIL>",
      status: "qualified",
      source: "marketplace"
    )
    
    get government_interests_path
    assert_response :success
    
    # Check status indicators
    assert_includes response.body, "Pending"
    assert_includes response.body, "Contacted"
    assert_includes response.body, "Qualified"
    assert_includes response.body, "Waiting for vendor response"
    assert_includes response.body, "Vendor contacted"
    assert_includes response.body, "Qualified lead"
  end
end