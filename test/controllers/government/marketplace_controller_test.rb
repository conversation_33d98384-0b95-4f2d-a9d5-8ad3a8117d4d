require "test_helper"

class Government::MarketplaceControllerTest < ActionDispatch::IntegrationTest
  def setup
    @gov_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "User",
      confirmed_at: 1.week.ago
    )
    
    @gov_account = Account.create!(
      name: "City Government",
      account_type: "government",
      status: "approved",
      owner: @gov_user,
      approved_at: 1.week.ago
    )
    
    AccountUser.create!(
      account: @gov_account,
      user: @gov_user,
      role: "admin",
      joined_at: 1.week.ago
    )
    
    @vendor_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Vendor",
      last_name: "User",
      confirmed_at: 1.week.ago
    )
    
    @vendor_account = Account.create!(
      name: "Tech Vendor",
      account_type: "vendor",
      status: "approved",
      owner: @vendor_user,
      approved_at: 1.week.ago
    )
    
    @company_profile = CompanyProfile.create!(
      account: @vendor_account,
      company_name: "TechCorp Solutions",
      published: true
    )
    
    @category = Category.create!(name: "Software")
    @tag = Tag.create!(name: "Cloud")
    
    @product = Product.create!(
      name: "Test Product",
      description: "A great product for testing",
      account: @vendor_account,
      pricing_model: "monthly",
      published: true
    )
    
    @product.categories << @category
    @product.tags << @tag
    
    @case_study = CaseStudy.create!(
      product: @product,
      title: "Success Story",
      client_name: "Test Client",
      results_summary: "Great results achieved",
      attachment_type: "pdf"
    )
    
    # Sign in the government user
    post session_path, params: { 
      email_address: @gov_user.email_address, 
      password: "password123" 
    }
  end

  test "should get marketplace index" do
    get marketplace_path
    assert_response :success
    assert_includes response.body, "Discover Government Technology Solutions"
    assert_includes response.body, @product.name
    assert_includes response.body, @company_profile.company_name
  end

  test "should get products listing" do
    get marketplace_products_path
    assert_response :success
    assert_includes response.body, @product.name
    assert_includes response.body, "Browse Products"
  end

  test "should filter products by search" do
    get marketplace_products_path(search: "Test Product")
    assert_response :success
    assert_includes response.body, @product.name
    
    get marketplace_products_path(search: "Nonexistent")
    assert_response :success
    assert_not_includes response.body, @product.name
  end

  test "should filter products by category" do
    get marketplace_products_path(category_id: @category.id)
    assert_response :success
    assert_includes response.body, @product.name
    
    other_category = Category.create!(name: "Hardware")
    assert_response :success
    get marketplace_products_path(category_id: other_category.id)
    assert_not_includes response.body, @product.name
  end

  test "should filter products by pricing model" do
    get marketplace_products_path(pricing_model: "monthly")
    assert_response :success
    assert_includes response.body, @product.name
    
    get marketplace_products_path(pricing_model: "annual")
    assert_response :success
    assert_not_includes response.body, @product.name
  end

  test "should sort products" do
    product2 = Product.create!(
      name: "Another Product",
      account: @vendor_account,
      pricing_model: "annual",
      published: true
    )
    
    get marketplace_products_path(sort: "name_asc")
    assert_response :success
    
    get marketplace_products_path(sort: "newest")
    assert_response :success
  end

  test "should get companies listing" do
    get marketplace_companies_path
    assert_response :success
    assert_includes response.body, @company_profile.company_name
  end

  test "should get company details" do
    get government_company_path(@company_profile)
    assert_response :success
    assert_includes response.body, @company_profile.company_name
    assert_includes response.body, @product.name
  end

  test "should get product details" do
    get marketplace_product_path(@product)
    assert_response :success
    assert_includes response.body, @product.name
    assert_includes response.body, @product.description.to_plain_text
    assert_includes response.body, "Ping Vendor"
    assert_includes response.body, @case_study.title
  end

  test "should show related products on product page" do
    related_product = Product.create!(
      name: "Related Product",
      account: @vendor_account,
      pricing_model: "annual",
      published: true
    )
    
    get marketplace_product_path(@product)
    assert_response :success
    assert_includes response.body, related_product.name
  end

  test "should require government authentication" do
    # Sign out
    delete session_path
    
    get marketplace_path
    assert_redirected_to new_session_path
  end

  test "should only show published products and companies" do
    unpublished_product = Product.create!(
      name: "Unpublished Product",
      account: @vendor_account,
      pricing_model: "monthly",
      published: false
    )
    
    vendor_account2 = Account.create!(
      name: "Tech Vendor 2",
      account_type: "vendor",
      status: "approved",
      owner: @vendor_user,
      approved_at: 1.week.ago
    )
    
    unpublished_company = CompanyProfile.create!(
      account: vendor_account2,
      company_name: "Unpublished Company",
      published: false
    )
    
    get marketplace_path
    assert_response :success
    assert_not_includes response.body, unpublished_product.name
    assert_not_includes response.body, unpublished_company.company_name
    
    get marketplace_products_path
    assert_response :success
    assert_not_includes response.body, unpublished_product.name
  end

  test "should handle multiple category filters" do
    category2 = Category.create!(name: "Hardware")
    @product.categories << category2
    
    get marketplace_products_path(category_ids: [@category.id, category2.id])
    assert_response :success
    assert_includes response.body, @product.name
  end

  test "should search across multiple fields" do
    # Search in product name
    get marketplace_products_path(search: "Test")
    assert_response :success
    assert_includes response.body, @product.name
    
    # Search in company name
    get marketplace_products_path(search: "TechCorp")
    assert_response :success
    assert_includes response.body, @product.name
    
    # Search in product name (partial)
    get marketplace_products_path(search: "Test")
    assert_response :success
    assert_includes response.body, @product.name
  end
end