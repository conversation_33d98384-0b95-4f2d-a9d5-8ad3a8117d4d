{"RSpec": {"coverage": {"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/base_controller.rb": {"lines": [1, 1, 1, 1, null, 1, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/application_controller.rb": {"lines": [1, 1, 1, 1, null, null, null, 1, null, 1, null, 1, 0, null, null, 1, 0, null, 0, 0, null, 0, null, null, null, 1, 0, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, 0, 0, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/application_helper.rb": {"lines": [1, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/account_confirmations_helper.rb": {"lines": [1, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/caching_helper.rb": {"lines": [1, null, 1, 0, 0, null, 0, null, null, null, 1, null, 0, null, null, null, null, null, null, null, 1, null, 0, null, null, null, null, null, null, null, 1, null, 0, null, null, null, null, null, null, null, 1, null, 0, null, null, null, null, null, null, null, 1, null, 0, null, null, null, null, null, null, null, null, 1, null, 1, 0, null, null, 1, 0, null, null, 0, null, null, 1, 0, 0, 0, 0, 0, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/invitation_acceptances_helper.rb": {"lines": [1, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/pages_helper.rb": {"lines": [1, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/registrations_helper.rb": {"lines": [1, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/seo_helper.rb": {"lines": [1, 1, 12, 12, 12, null, 0, null, null, null, 1, 12, null, null, 1, 4, 4, 0, null, 4, null, null, null, 1, 8, null, null, 1, 8, null, null, 1, null, 4, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, 0, null, 0, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, null, null, null, null, null, null, null, null, null, 1, null, 4, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, 8, 8, null, null, null, 1, 0, null, 0, null, null, null, null, null, null, null, 0, null, null, null, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/authentication.rb": {"lines": [1, 1, null, 1, 1, 1, 1, null, null, 1, 1, 1, null, null, null, 1, 1, 0, null, null, 1, 4, null, null, 1, 8, null, null, 1, 8, null, null, 1, 4, 4, null, null, 1, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, null, null, null, 1, 0, 0, 0, null, null, 0, null, null, null, 1, 0, 0, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/security_headers.rb": {"lines": [1, 1, null, 1, 1, 1, null, null, 1, null, 1, null, 8, null, null, 8, 8, 8, 8, 8, null, null, 8, 0, null, null, null, 1, null, null, 8, null, null, null, null, null, null, null, null, null, null, null, null, 8, null, null, 1, null, null, 8, null, null, null, null, 8, 72, null, null, null, 8, null, null, 1, 72, null, 72, 72, 144, 0, 144, 0, null, null, null, null, 1, 8, null, 8, 16, null, null, 16, null, null, 16, null, null, 0, null, null, 0, 0, 0, 0, 0, 0, null, null, null, null, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/error_handling.rb": {"lines": [1, 1, null, 1, null, 1, 1, 1, 1, 1, null, null, 1, 0, null, null, null, 1, null, 1, 0, null, 0, 0, 0, null, null, null, 1, 0, null, null, null, null, null, 0, 0, 0, 0, null, 0, null, null, null, 1, 0, null, 0, 0, 0, 0, null, 0, null, null, null, 1, 0, null, 0, 0, 0, 0, null, 0, 0, null, null, null, null, null, null, null, 1, 0, null, null, null, null, null, null, 0, 0, 0, 0, null, 0, null, null, null, 1, 0, null, null, 0, 0, null, null, 0, 0, 0, null, null, null, 1, null, 0, null, null, null, null, null, null, null, null, null, null, null, 0, null, 0, null, 0, null, null, null, 1, 0, 0, null, 0, null, null, null, 1, null, 0, 0, 0, null, null, null, 0, null, null, 1, null, null, 0, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/user.rb": {"lines": [1, 1, 1, null, 1, null, 1, null, 1, 0, 0, null, null, 1, 0, 0, null, null, 1, 0, 0, null, null, 1, 0, 0, null, null, null, 1, 1, 1, null, null, 1, null, 9, null, 1, 1, 1, 1, 1, null, 1, 0, null, null, 1, 0, null, null, 1, null, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 1, 1, null, 1, 0, null, null, 1, 0, null, null, null, 1, 0, null, null, 1, 0, null, null, 1, null, 1, 4, null, null, 0, 0, null, null, null, 0, 0, 0, null, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/application_record.rb": {"lines": [1, 1, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/account.rb": {"lines": [1, 1, 1, null, 1, 1, 1, 1, 1, 1, 1, 1, 1, null, 1, 1, 1, null, 1, null, 1, 1, null, 1, 1, 1, 1, 1, 1, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, null, null, null, null, 1, 0, null, null, null, null, null, null, 1, 0, null, null, 1, 0, 0, null, null, 1, null, 1, 4, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/account_user.rb": {"lines": [1, 1, 1, null, 1, 1, null, 1, null, 1, 1, null, 1, 0, null, null, 1, 0, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/company_profile.rb": {"lines": [1, 1, 1, null, 1, 1, 1, 1, null, 1, 1, 1, 1, 1, null, null, 1, null, null, null, null, null, null, null, 1, null, 1, 0, null, null, 1, 0, 0, null, 0, null, null, 1, 0, null, null, 1, 0, 0, null, 0, null, null, 1, 0, null, null, 1, 0, 0, null, null, 1, 0, 0, null, null, 1, 0, 0, null, null, 1, 0, 0, null, null, 1, null, 1, 4, null, null, 0, 0, null, null, null, 0, 0, 0, null, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/company_profiles_controller.rb": {"lines": [1, 1, 1, 1, null, 1, null, null, 1, 0, null, null, 1, 0, null, 0, 0, null, 0, null, null, null, 1, null, null, 1, 0, 0, null, 0, null, null, null, 1, 0, 0, null, null, 1, 0, 0, null, 0, null, null, null, 1, 0, 0, null, null, 1, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, null, null, null, null, null, 1, 0, null, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/base_controller.rb": {"lines": [1, 1, 1, 1, 1, null, 1, null, 1, null, 1, 0, null, null, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/current.rb": {"lines": [1, 1, 1, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/sessions_controller.rb": {"lines": [1, 1, 1, null, 1, null, null, 1, 0, null, 0, 0, 0, 0, null, null, null, 0, 0, 0, 0, null, 0, null, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, null, 0, null, null, null, null, 0, 0, null, 0, 0, null, 0, 0, null, 0, null, null, 0, null, null, 0, null, null, null, 1, 0, 0, null, null]}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/channels/application_cable/connection.rb": {"lines": [0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/account_confirmations_controller.rb": {"lines": [0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts/team_invitations_controller.rb": {"lines": [0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts/team_members_controller.rb": {"lines": [0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts_controller.rb": {"lines": [0, 0, null, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/accounts_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, null, 0, null, null, null, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/email_previews_controller.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/health_controller.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/overview_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/base_controller.rb": {"lines": [0, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/dashboard_controller.rb": {"lines": [0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/interests_controller.rb": {"lines": [0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/errors_controller.rb": {"lines": [0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/home_controller.rb": {"lines": [0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/invitation_acceptances_controller.rb": {"lines": [0, 0, null, 0, 0, null, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/marketplace_controller.rb": {"lines": [0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, null, 0, null, null, 0, null, null, 0, null, null, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/pages_controller.rb": {"lines": [0, 0, null, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/passwords_controller.rb": {"lines": [0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/registrations_controller.rb": {"lines": [0, 0, null, 0, 0, 0, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, null, 0, null, null, 0, null, null, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/sitemaps_controller.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, null, null, 0, 0, null, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/leads_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/overview_controller.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/products_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/jobs/application_job.rb": {"lines": [0, null, null, null, null, null, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/account_confirmation_mailer.rb": {"lines": [0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/admin_mailer.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/application_mailer.rb": {"lines": [0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/lead_mailer.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/passwords_mailer.rb": {"lines": [0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/team_invitation_mailer.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/user_mailer.rb": {"lines": [0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/case_study.rb": {"lines": [0, 0, 0, 0, null, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/category.rb": {"lines": [0, 0, 0, null, 0, 0, null, 0, null, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/concerns/performance_optimizable.rb": {"lines": [0, 0, null, 0, null, 0, null, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, 0, null, 0, null, 0, null, 0, null, 0, null, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/lead.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product.rb": {"lines": [0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_category.rb": {"lines": [0, 0, 0, null, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_medium.rb": {"lines": [0, 0, 0, null, 0, 0, null, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_tag.rb": {"lines": [0, 0, 0, null, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_video.rb": {"lines": [0, 0, 0, null, 0, 0, null, 0, null, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/session.rb": {"lines": [0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/tag.rb": {"lines": [0, 0, 0, null, 0, 0, null, 0, null, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/team_invitation.rb": {"lines": [0, 0, 0, null, 0, 0, 0, null, 0, null, 0, null, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/services/security_logger.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/validators/password_strength_validator.rb": {"lines": [0, 0, 0, null, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, 0, 0, 0], "branches": {}}}, "timestamp": 1752060811}}